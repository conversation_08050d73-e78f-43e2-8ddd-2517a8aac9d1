using System;
using System.Collections.Generic;
using System.Threading;
using UnityEngine;
using DGame.Framework;

namespace Storage
{
    /// <summary>
    /// 存储系统集合操作的静态委托方法
    /// 避免闭包和内存分配，提供最高性能的集合操作
    /// </summary>
    public static class StorageCollectionDelegates
    {
        #region List<int> 静态方法

        public static int GetListIntCount(object obj) => ((List<int>)obj).Count;
        public static object GetListIntItem(object obj, int index) => ((List<int>)obj)[index];
        public static void AddToListInt(object obj, object item) => ((List<int>)obj).Add((int)item);

        #endregion

        #region List<float> 静态方法

        public static int GetListFloatCount(object obj) => ((List<float>)obj).Count;
        public static object GetListFloatItem(object obj, int index) => ((List<float>)obj)[index];
        public static void AddToListFloat(object obj, object item) => ((List<float>)obj).Add((float)item);

        #endregion

        #region List<double> 静态方法

        public static int GetListDoubleCount(object obj) => ((List<double>)obj).Count;
        public static object GetListDoubleItem(object obj, int index) => ((List<double>)obj)[index];
        public static void AddToListDouble(object obj, object item) => ((List<double>)obj).Add((double)item);

        #endregion

        #region List<bool> 静态方法

        public static int GetListBoolCount(object obj) => ((List<bool>)obj).Count;
        public static object GetListBoolItem(object obj, int index) => ((List<bool>)obj)[index];
        public static void AddToListBool(object obj, object item) => ((List<bool>)obj).Add((bool)item);

        #endregion

        #region List<string> 静态方法

        public static int GetListStringCount(object obj) => ((List<string>)obj).Count;
        public static object GetListStringItem(object obj, int index) => ((List<string>)obj)[index];
        public static void AddToListString(object obj, object item) => ((List<string>)obj).Add((string)item);

        #endregion

        #region List<long> 静态方法

        public static int GetListLongCount(object obj) => ((List<long>)obj).Count;
        public static object GetListLongItem(object obj, int index) => ((List<long>)obj)[index];
        public static void AddToListLong(object obj, object item) => ((List<long>)obj).Add((long)item);

        #endregion

        #region List<Vector2> 静态方法

        public static int GetListVector2Count(object obj) => ((List<Vector2>)obj).Count;
        public static object GetListVector2Item(object obj, int index) => ((List<Vector2>)obj)[index];
        public static void AddToListVector2(object obj, object item) => ((List<Vector2>)obj).Add((Vector2)item);

        #endregion

        #region List<Vector3> 静态方法

        public static int GetListVector3Count(object obj) => ((List<Vector3>)obj).Count;
        public static object GetListVector3Item(object obj, int index) => ((List<Vector3>)obj)[index];
        public static void AddToListVector3(object obj, object item) => ((List<Vector3>)obj).Add((Vector3)item);

        #endregion

        #region List<Color> 静态方法

        public static int GetListColorCount(object obj) => ((List<Color>)obj).Count;
        public static object GetListColorItem(object obj, int index) => ((List<Color>)obj)[index];
        public static void AddToListColor(object obj, object item) => ((List<Color>)obj).Add((Color)item);

        #endregion

        #region List<byte> 静态方法

        public static int GetListByteCount(object obj) => ((List<byte>)obj).Count;
        public static object GetListByteItem(object obj, int index) => ((List<byte>)obj)[index];
        public static void AddToListByte(object obj, object item) => ((List<byte>)obj).Add((byte)item);

        #endregion

        #region List<sbyte> 静态方法

        public static int GetListSByteCount(object obj) => ((List<sbyte>)obj).Count;
        public static object GetListSByteItem(object obj, int index) => ((List<sbyte>)obj)[index];
        public static void AddToListSByte(object obj, object item) => ((List<sbyte>)obj).Add((sbyte)item);

        #endregion

        #region List<short> 静态方法

        public static int GetListShortCount(object obj) => ((List<short>)obj).Count;
        public static object GetListShortItem(object obj, int index) => ((List<short>)obj)[index];
        public static void AddToListShort(object obj, object item) => ((List<short>)obj).Add((short)item);

        #endregion

        #region List<ushort> 静态方法

        public static int GetListUShortCount(object obj) => ((List<ushort>)obj).Count;
        public static object GetListUShortItem(object obj, int index) => ((List<ushort>)obj)[index];
        public static void AddToListUShort(object obj, object item) => ((List<ushort>)obj).Add((ushort)item);

        #endregion

        #region List<uint> 静态方法

        public static int GetListUIntCount(object obj) => ((List<uint>)obj).Count;
        public static object GetListUIntItem(object obj, int index) => ((List<uint>)obj)[index];
        public static void AddToListUInt(object obj, object item) => ((List<uint>)obj).Add((uint)item);

        #endregion

        #region List<ulong> 静态方法

        public static int GetListULongCount(object obj) => ((List<ulong>)obj).Count;
        public static object GetListULongItem(object obj, int index) => ((List<ulong>)obj)[index];
        public static void AddToListULong(object obj, object item) => ((List<ulong>)obj).Add((ulong)item);

        #endregion

        #region List<decimal> 静态方法

        public static int GetListDecimalCount(object obj) => ((List<decimal>)obj).Count;
        public static object GetListDecimalItem(object obj, int index) => ((List<decimal>)obj)[index];
        public static void AddToListDecimal(object obj, object item) => ((List<decimal>)obj).Add((decimal)item);

        #endregion

        #region List<Vector4> 静态方法

        public static int GetListVector4Count(object obj) => ((List<Vector4>)obj).Count;
        public static object GetListVector4Item(object obj, int index) => ((List<Vector4>)obj)[index];
        public static void AddToListVector4(object obj, object item) => ((List<Vector4>)obj).Add((Vector4)item);

        #endregion

        #region List<Quaternion> 静态方法

        public static int GetListQuaternionCount(object obj) => ((List<Quaternion>)obj).Count;
        public static object GetListQuaternionItem(object obj, int index) => ((List<Quaternion>)obj)[index];
        public static void AddToListQuaternion(object obj, object item) => ((List<Quaternion>)obj).Add((Quaternion)item);

        #endregion

        #region List<Color32> 静态方法

        public static int GetListColor32Count(object obj) => ((List<Color32>)obj).Count;
        public static object GetListColor32Item(object obj, int index) => ((List<Color32>)obj)[index];
        public static void AddToListColor32(object obj, object item) => ((List<Color32>)obj).Add((Color32)item);

        #endregion

        #region Dictionary<string, int> 静态方法

        public static System.Collections.IEnumerable GetDictStringIntKeys(object obj) => ((Dictionary<string, int>)obj).Keys;
        public static object GetDictStringIntItem(object obj, object key) => ((Dictionary<string, int>)obj)[(string)key];
        public static void AddToDictStringInt(object obj, object key, object value) => ((Dictionary<string, int>)obj).Add((string)key, (int)value);

        #endregion

        #region Dictionary<string, float> 静态方法

        public static System.Collections.IEnumerable GetDictStringFloatKeys(object obj) => ((Dictionary<string, float>)obj).Keys;
        public static object GetDictStringFloatItem(object obj, object key) => ((Dictionary<string, float>)obj)[(string)key];
        public static void AddToDictStringFloat(object obj, object key, object value) => ((Dictionary<string, float>)obj).Add((string)key, (float)value);

        #endregion

        #region Dictionary<string, string> 静态方法

        public static System.Collections.IEnumerable GetDictStringStringKeys(object obj) => ((Dictionary<string, string>)obj).Keys;
        public static object GetDictStringStringItem(object obj, object key) => ((Dictionary<string, string>)obj)[(string)key];
        public static void AddToDictStringString(object obj, object key, object value) => ((Dictionary<string, string>)obj).Add((string)key, (string)value);

        #endregion

        #region Dictionary<string, Vector3> 静态方法

        public static System.Collections.IEnumerable GetDictStringVector3Keys(object obj) => ((Dictionary<string, Vector3>)obj).Keys;
        public static object GetDictStringVector3Item(object obj, object key) => ((Dictionary<string, Vector3>)obj)[(string)key];
        public static void AddToDictStringVector3(object obj, object key, object value) => ((Dictionary<string, Vector3>)obj).Add((string)key, (Vector3)value);

        #endregion

        #region Dictionary<string, Color> 静态方法

        public static System.Collections.IEnumerable GetDictStringColorKeys(object obj) => ((Dictionary<string, Color>)obj).Keys;
        public static object GetDictStringColorItem(object obj, object key) => ((Dictionary<string, Color>)obj)[(string)key];
        public static void AddToDictStringColor(object obj, object key, object value) => ((Dictionary<string, Color>)obj).Add((string)key, (Color)value);

        #endregion

        #region Dictionary<int, int> 静态方法

        public static System.Collections.IEnumerable GetDictIntIntKeys(object obj) => ((Dictionary<int, int>)obj).Keys;
        public static object GetDictIntIntItem(object obj, object key) => ((Dictionary<int, int>)obj)[(int)key];
        public static void AddToDictIntInt(object obj, object key, object value) => ((Dictionary<int, int>)obj).Add((int)key, (int)value);

        #endregion

        #region Dictionary<int, string> 静态方法

        public static System.Collections.IEnumerable GetDictIntStringKeys(object obj) => ((Dictionary<int, string>)obj).Keys;
        public static object GetDictIntStringItem(object obj, object key) => ((Dictionary<int, string>)obj)[(int)key];
        public static void AddToDictIntString(object obj, object key, object value) => ((Dictionary<int, string>)obj).Add((int)key, (string)value);

        #endregion

        #region Dictionary<string, double> 静态方法

        public static System.Collections.IEnumerable GetDictStringDoubleKeys(object obj) => ((Dictionary<string, double>)obj).Keys;
        public static object GetDictStringDoubleItem(object obj, object key) => ((Dictionary<string, double>)obj)[(string)key];
        public static void AddToDictStringDouble(object obj, object key, object value) => ((Dictionary<string, double>)obj).Add((string)key, (double)value);

        #endregion

        #region Dictionary<string, bool> 静态方法

        public static System.Collections.IEnumerable GetDictStringBoolKeys(object obj) => ((Dictionary<string, bool>)obj).Keys;
        public static object GetDictStringBoolItem(object obj, object key) => ((Dictionary<string, bool>)obj)[(string)key];
        public static void AddToDictStringBool(object obj, object key, object value) => ((Dictionary<string, bool>)obj).Add((string)key, (bool)value);

        #endregion

        #region Dictionary<string, long> 静态方法

        public static System.Collections.IEnumerable GetDictStringLongKeys(object obj) => ((Dictionary<string, long>)obj).Keys;
        public static object GetDictStringLongItem(object obj, object key) => ((Dictionary<string, long>)obj)[(string)key];
        public static void AddToDictStringLong(object obj, object key, object value) => ((Dictionary<string, long>)obj).Add((string)key, (long)value);

        #endregion

        #region Dictionary<string, Vector2> 静态方法

        public static System.Collections.IEnumerable GetDictStringVector2Keys(object obj) => ((Dictionary<string, Vector2>)obj).Keys;
        public static object GetDictStringVector2Item(object obj, object key) => ((Dictionary<string, Vector2>)obj)[(string)key];
        public static void AddToDictStringVector2(object obj, object key, object value) => ((Dictionary<string, Vector2>)obj).Add((string)key, (Vector2)value);

        #endregion

        #region Dictionary<string, Vector4> 静态方法

        public static System.Collections.IEnumerable GetDictStringVector4Keys(object obj) => ((Dictionary<string, Vector4>)obj).Keys;
        public static object GetDictStringVector4Item(object obj, object key) => ((Dictionary<string, Vector4>)obj)[(string)key];
        public static void AddToDictStringVector4(object obj, object key, object value) => ((Dictionary<string, Vector4>)obj).Add((string)key, (Vector4)value);

        #endregion

        #region Dictionary<string, Quaternion> 静态方法

        public static System.Collections.IEnumerable GetDictStringQuaternionKeys(object obj) => ((Dictionary<string, Quaternion>)obj).Keys;
        public static object GetDictStringQuaternionItem(object obj, object key) => ((Dictionary<string, Quaternion>)obj)[(string)key];
        public static void AddToDictStringQuaternion(object obj, object key, object value) => ((Dictionary<string, Quaternion>)obj).Add((string)key, (Quaternion)value);

        #endregion

        #region Dictionary<string, Color32> 静态方法

        public static System.Collections.IEnumerable GetDictStringColor32Keys(object obj) => ((Dictionary<string, Color32>)obj).Keys;
        public static object GetDictStringColor32Item(object obj, object key) => ((Dictionary<string, Color32>)obj)[(string)key];
        public static void AddToDictStringColor32(object obj, object key, object value) => ((Dictionary<string, Color32>)obj).Add((string)key, (Color32)value);

        #endregion

        #region Dictionary<int, float> 静态方法

        public static System.Collections.IEnumerable GetDictIntFloatKeys(object obj) => ((Dictionary<int, float>)obj).Keys;
        public static object GetDictIntFloatItem(object obj, object key) => ((Dictionary<int, float>)obj)[(int)key];
        public static void AddToDictIntFloat(object obj, object key, object value) => ((Dictionary<int, float>)obj).Add((int)key, (float)value);

        #endregion

        #region Dictionary<int, double> 静态方法

        public static System.Collections.IEnumerable GetDictIntDoubleKeys(object obj) => ((Dictionary<int, double>)obj).Keys;
        public static object GetDictIntDoubleItem(object obj, object key) => ((Dictionary<int, double>)obj)[(int)key];
        public static void AddToDictIntDouble(object obj, object key, object value) => ((Dictionary<int, double>)obj).Add((int)key, (double)value);

        #endregion

        #region Dictionary<int, bool> 静态方法

        public static System.Collections.IEnumerable GetDictIntBoolKeys(object obj) => ((Dictionary<int, bool>)obj).Keys;
        public static object GetDictIntBoolItem(object obj, object key) => ((Dictionary<int, bool>)obj)[(int)key];
        public static void AddToDictIntBool(object obj, object key, object value) => ((Dictionary<int, bool>)obj).Add((int)key, (bool)value);

        #endregion

        #region Dictionary<int, Vector2> 静态方法

        public static System.Collections.IEnumerable GetDictIntVector2Keys(object obj) => ((Dictionary<int, Vector2>)obj).Keys;
        public static object GetDictIntVector2Item(object obj, object key) => ((Dictionary<int, Vector2>)obj)[(int)key];
        public static void AddToDictIntVector2(object obj, object key, object value) => ((Dictionary<int, Vector2>)obj).Add((int)key, (Vector2)value);

        #endregion

        #region Dictionary<int, Vector3> 静态方法

        public static System.Collections.IEnumerable GetDictIntVector3Keys(object obj) => ((Dictionary<int, Vector3>)obj).Keys;
        public static object GetDictIntVector3Item(object obj, object key) => ((Dictionary<int, Vector3>)obj)[(int)key];
        public static void AddToDictIntVector3(object obj, object key, object value) => ((Dictionary<int, Vector3>)obj).Add((int)key, (Vector3)value);

        #endregion

        #region 类型工厂静态方法

        // 基础类型工厂
        public static StorageTypeWrapper CreateIntWrapper() => new StorageTypeWrapper<int>();
        public static StorageTypeWrapper CreateFloatWrapper() => new StorageTypeWrapper<float>();
        public static StorageTypeWrapper CreateDoubleWrapper() => new StorageTypeWrapper<double>();
        public static StorageTypeWrapper CreateBoolWrapper() => new StorageTypeWrapper<bool>();
        public static StorageTypeWrapper CreateStringWrapper() => new StorageTypeWrapper<string>();
        public static StorageTypeWrapper CreateLongWrapper() => new StorageTypeWrapper<long>();
        public static StorageTypeWrapper CreateCharWrapper() => new StorageTypeWrapper<char>();
        public static StorageTypeWrapper CreateByteWrapper() => new StorageTypeWrapper<byte>();
        public static StorageTypeWrapper CreateSByteWrapper() => new StorageTypeWrapper<sbyte>();
        public static StorageTypeWrapper CreateShortWrapper() => new StorageTypeWrapper<short>();
        public static StorageTypeWrapper CreateUShortWrapper() => new StorageTypeWrapper<ushort>();
        public static StorageTypeWrapper CreateUIntWrapper() => new StorageTypeWrapper<uint>();
        public static StorageTypeWrapper CreateULongWrapper() => new StorageTypeWrapper<ulong>();
        public static StorageTypeWrapper CreateDecimalWrapper() => new StorageTypeWrapper<decimal>();
        public static StorageTypeWrapper CreateDateTimeWrapper() => new StorageTypeWrapper<DateTime>();
        public static StorageTypeWrapper CreateGuidWrapper() => new StorageTypeWrapper<Guid>();

        // Unity类型工厂
        public static StorageTypeWrapper CreateVector2Wrapper() => new StorageTypeWrapper<Vector2>();
        public static StorageTypeWrapper CreateVector3Wrapper() => new StorageTypeWrapper<Vector3>();
        public static StorageTypeWrapper CreateVector4Wrapper() => new StorageTypeWrapper<Vector4>();
        public static StorageTypeWrapper CreateQuaternionWrapper() => new StorageTypeWrapper<Quaternion>();
        public static StorageTypeWrapper CreateColorWrapper() => new StorageTypeWrapper<Color>();
        public static StorageTypeWrapper CreateColor32Wrapper() => new StorageTypeWrapper<Color32>();
        public static StorageTypeWrapper CreateRectWrapper() => new StorageTypeWrapper<Rect>();
        public static StorageTypeWrapper CreateRectIntWrapper() => new StorageTypeWrapper<RectInt>();
        public static StorageTypeWrapper CreateBoundsWrapper() => new StorageTypeWrapper<Bounds>();
        public static StorageTypeWrapper CreateBoundsIntWrapper() => new StorageTypeWrapper<BoundsInt>();
        public static StorageTypeWrapper CreateVector2IntWrapper() => new StorageTypeWrapper<Vector2Int>();
        public static StorageTypeWrapper CreateVector3IntWrapper() => new StorageTypeWrapper<Vector3Int>();
        public static StorageTypeWrapper CreateMatrix4x4Wrapper() => new StorageTypeWrapper<Matrix4x4>();
        public static StorageTypeWrapper CreateLayerMaskWrapper() => new StorageTypeWrapper<LayerMask>();

        // 数组类型工厂
        public static StorageTypeWrapper CreateIntArrayWrapper() => new StorageTypeWrapper<int[]>();
        public static StorageTypeWrapper CreateFloatArrayWrapper() => new StorageTypeWrapper<float[]>();
        public static StorageTypeWrapper CreateBoolArrayWrapper() => new StorageTypeWrapper<bool[]>();
        public static StorageTypeWrapper CreateStringArrayWrapper() => new StorageTypeWrapper<string[]>();
        public static StorageTypeWrapper CreateLongArrayWrapper() => new StorageTypeWrapper<long[]>();
        public static StorageTypeWrapper CreateDoubleArrayWrapper() => new StorageTypeWrapper<double[]>();
        public static StorageTypeWrapper CreateCharArrayWrapper() => new StorageTypeWrapper<char[]>();
        public static StorageTypeWrapper CreateByteArrayWrapper() => new StorageTypeWrapper<byte[]>();
        public static StorageTypeWrapper CreateSByteArrayWrapper() => new StorageTypeWrapper<sbyte[]>();
        public static StorageTypeWrapper CreateShortArrayWrapper() => new StorageTypeWrapper<short[]>();
        public static StorageTypeWrapper CreateUShortArrayWrapper() => new StorageTypeWrapper<ushort[]>();
        public static StorageTypeWrapper CreateUIntArrayWrapper() => new StorageTypeWrapper<uint[]>();
        public static StorageTypeWrapper CreateULongArrayWrapper() => new StorageTypeWrapper<ulong[]>();
        public static StorageTypeWrapper CreateDecimalArrayWrapper() => new StorageTypeWrapper<decimal[]>();
        public static StorageTypeWrapper CreateVector2ArrayWrapper() => new StorageTypeWrapper<Vector2[]>();
        public static StorageTypeWrapper CreateVector3ArrayWrapper() => new StorageTypeWrapper<Vector3[]>();
        public static StorageTypeWrapper CreateVector4ArrayWrapper() => new StorageTypeWrapper<Vector4[]>();
        public static StorageTypeWrapper CreateQuaternionArrayWrapper() => new StorageTypeWrapper<Quaternion[]>();
        public static StorageTypeWrapper CreateColorArrayWrapper() => new StorageTypeWrapper<Color[]>();
        public static StorageTypeWrapper CreateColor32ArrayWrapper() => new StorageTypeWrapper<Color32[]>();
        public static StorageTypeWrapper CreateRectArrayWrapper() => new StorageTypeWrapper<Rect[]>();

        // List 集合类型工厂
        public static StorageTypeWrapper CreateListIntWrapper() => new StorageTypeWrapper<List<int>>();
        public static StorageTypeWrapper CreateListFloatWrapper() => new StorageTypeWrapper<List<float>>();
        public static StorageTypeWrapper CreateListDoubleWrapper() => new StorageTypeWrapper<List<double>>();
        public static StorageTypeWrapper CreateListBoolWrapper() => new StorageTypeWrapper<List<bool>>();
        public static StorageTypeWrapper CreateListStringWrapper() => new StorageTypeWrapper<List<string>>();
        public static StorageTypeWrapper CreateListLongWrapper() => new StorageTypeWrapper<List<long>>();
        public static StorageTypeWrapper CreateListCharWrapper() => new StorageTypeWrapper<List<char>>();
        public static StorageTypeWrapper CreateListByteWrapper() => new StorageTypeWrapper<List<byte>>();
        public static StorageTypeWrapper CreateListSByteWrapper() => new StorageTypeWrapper<List<sbyte>>();
        public static StorageTypeWrapper CreateListShortWrapper() => new StorageTypeWrapper<List<short>>();
        public static StorageTypeWrapper CreateListUShortWrapper() => new StorageTypeWrapper<List<ushort>>();
        public static StorageTypeWrapper CreateListUIntWrapper() => new StorageTypeWrapper<List<uint>>();
        public static StorageTypeWrapper CreateListULongWrapper() => new StorageTypeWrapper<List<ulong>>();
        public static StorageTypeWrapper CreateListDecimalWrapper() => new StorageTypeWrapper<List<decimal>>();
        public static StorageTypeWrapper CreateListVector2Wrapper() => new StorageTypeWrapper<List<Vector2>>();
        public static StorageTypeWrapper CreateListVector3Wrapper() => new StorageTypeWrapper<List<Vector3>>();
        public static StorageTypeWrapper CreateListVector4Wrapper() => new StorageTypeWrapper<List<Vector4>>();
        public static StorageTypeWrapper CreateListQuaternionWrapper() => new StorageTypeWrapper<List<Quaternion>>();
        public static StorageTypeWrapper CreateListColorWrapper() => new StorageTypeWrapper<List<Color>>();
        public static StorageTypeWrapper CreateListColor32Wrapper() => new StorageTypeWrapper<List<Color32>>();

        // Dictionary 集合类型工厂 (string键)
        public static StorageTypeWrapper CreateDictStringIntWrapper() => new StorageTypeWrapper<Dictionary<string, int>>();
        public static StorageTypeWrapper CreateDictStringFloatWrapper() => new StorageTypeWrapper<Dictionary<string, float>>();
        public static StorageTypeWrapper CreateDictStringDoubleWrapper() => new StorageTypeWrapper<Dictionary<string, double>>();
        public static StorageTypeWrapper CreateDictStringBoolWrapper() => new StorageTypeWrapper<Dictionary<string, bool>>();
        public static StorageTypeWrapper CreateDictStringStringWrapper() => new StorageTypeWrapper<Dictionary<string, string>>();
        public static StorageTypeWrapper CreateDictStringLongWrapper() => new StorageTypeWrapper<Dictionary<string, long>>();
        public static StorageTypeWrapper CreateDictStringVector2Wrapper() => new StorageTypeWrapper<Dictionary<string, Vector2>>();
        public static StorageTypeWrapper CreateDictStringVector3Wrapper() => new StorageTypeWrapper<Dictionary<string, Vector3>>();
        public static StorageTypeWrapper CreateDictStringVector4Wrapper() => new StorageTypeWrapper<Dictionary<string, Vector4>>();
        public static StorageTypeWrapper CreateDictStringQuaternionWrapper() => new StorageTypeWrapper<Dictionary<string, Quaternion>>();
        public static StorageTypeWrapper CreateDictStringColorWrapper() => new StorageTypeWrapper<Dictionary<string, Color>>();
        public static StorageTypeWrapper CreateDictStringColor32Wrapper() => new StorageTypeWrapper<Dictionary<string, Color32>>();

        // Dictionary 集合类型工厂 (int键)
        public static StorageTypeWrapper CreateDictIntIntWrapper() => new StorageTypeWrapper<Dictionary<int, int>>();
        public static StorageTypeWrapper CreateDictIntFloatWrapper() => new StorageTypeWrapper<Dictionary<int, float>>();
        public static StorageTypeWrapper CreateDictIntDoubleWrapper() => new StorageTypeWrapper<Dictionary<int, double>>();
        public static StorageTypeWrapper CreateDictIntBoolWrapper() => new StorageTypeWrapper<Dictionary<int, bool>>();
        public static StorageTypeWrapper CreateDictIntStringWrapper() => new StorageTypeWrapper<Dictionary<int, string>>();
        public static StorageTypeWrapper CreateDictIntVector2Wrapper() => new StorageTypeWrapper<Dictionary<int, Vector2>>();
        public static StorageTypeWrapper CreateDictIntVector3Wrapper() => new StorageTypeWrapper<Dictionary<int, Vector3>>();
        public static StorageTypeWrapper CreateDictIntColorWrapper() => new StorageTypeWrapper<Dictionary<int, Color>>();

        #endregion
    }
}
