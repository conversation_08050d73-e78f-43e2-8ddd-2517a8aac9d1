2025-06-01 03:41:30.9819 [INFO] [StorageTypeInitializer]::PreCacheCommonCollectionTypes(286) - Pre-cached 114 collection operation delegates for common types
2025-06-01 03:41:30.9910 [INFO] [StorageTypeInitializer]::.cctor(608) - StorageTypeInitializer static initialization completed with 90 supported types
2025-06-01 03:41:30.9910 [INFO] [Storage]::InitializeCore(68) - Storage core initialized successfully with 90 supported types and 0 preloaded conversion delegates
2025-06-01 03:41:30.9910 [INFO] [StorageSettings]::InitializePathCache(71) - Storage paths cached successfully
2025-06-01 03:41:31.0029 [INFO] [StorageManager]::Initialize(88) - StorageManager initialized successfully
2025-06-01 03:41:31.4830 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_BasicTypes
2025-06-01 03:41:31.4830 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testInt, Type: Int32, Value: 42
2025-06-01 03:41:31.8171 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testIntNegative, Type: Int32, Value: -123
2025-06-01 03:41:31.8615 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testIntZero, Type: Int32, Value: 0
2025-06-01 03:41:31.8898 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testIntMax, Type: Int32, Value: 2147483647
2025-06-01 03:41:31.9145 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testIntMin, Type: Int32, Value: -2147483648
2025-06-01 03:41:31.9145 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testFloat, Type: Single, Value: 3.14
2025-06-01 03:41:31.9339 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testFloatNegative, Type: Single, Value: -2.71
2025-06-01 03:41:31.9405 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testFloatZero, Type: Single, Value: 0
2025-06-01 03:41:31.9405 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testFloatMax, Type: Single, Value: 3.402823E+38
2025-06-01 03:41:31.9552 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testFloatMin, Type: Single, Value: -3.402823E+38
2025-06-01 03:41:31.9552 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testDouble, Type: Double, Value: 3.14159265358979
2025-06-01 03:41:31.9799 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testDoubleNegative, Type: Double, Value: -2.71828182845905
2025-06-01 03:41:31.9889 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testBoolTrue, Type: Boolean, Value: True
2025-06-01 03:41:31.9889 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testBoolFalse, Type: Boolean, Value: False
2025-06-01 03:41:32.0131 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testString, Type: String, Value: Hello World
2025-06-01 03:41:32.0246 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testStringEmpty, Type: String, Value: 
2025-06-01 03:41:32.0246 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testStringSpecial, Type: String, Value: 特殊字符：中文，符号!@#$%^&*()
2025-06-01 03:41:32.0418 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testStringUnicode, Type: String, Value: Unicode: 🎮🚀💻
2025-06-01 03:41:32.0498 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testStringJson, Type: String, Value: {"key": "value"}
2025-06-01 03:41:32.0498 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testByte, Type: Byte, Value: 255
2025-06-01 03:41:32.0744 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testShort, Type: Int16, Value: 32767
2025-06-01 03:41:32.0876 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testLong, Type: Int64, Value: 9223372036854775807
2025-06-01 03:41:32.0981 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testChar, Type: Char, Value: A
2025-06-01 03:41:32.1117 [INFO] [StorageInstance]::Set(103) - Instance [Test_BasicTypes] - Key: testDecimal, Type: Decimal, Value: 123.456789
2025-06-01 03:41:32.1117 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 24 items
2025-06-01 03:41:32.1117 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_BasicTypes] - Starting sync save operation
2025-06-01 03:41:32.1619 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_BasicTypes.json
2025-06-01 03:41:32.1619 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_BasicTypes] - Data saved synchronously
2025-06-01 03:41:32.1619 [INFO] [StorageCache]::Clear(177) - Cleared 24 items from cache
2025-06-01 03:41:32.1619 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_BasicTypes
2025-06-01 03:41:32.1619 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_BasicTypes
2025-06-01 03:41:32.2249 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_UnityTypes
2025-06-01 03:41:32.2249 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testVector2, Type: Vector2, Value: (1.50, 2.50)
2025-06-01 03:41:32.2249 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testVector3, Type: Vector3, Value: (1.00, 2.00, 3.00)
2025-06-01 03:41:32.2443 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testVector4, Type: Vector4, Value: (1.00, 2.00, 3.00, 4.00)
2025-06-01 03:41:32.2565 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testVector2Int, Type: Vector2Int, Value: (10, 20)
2025-06-01 03:41:32.2676 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testVector3Int, Type: Vector3Int, Value: (1, 2, 3)
2025-06-01 03:41:32.2676 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testQuaternion, Type: Quaternion, Value: (0.65328, -0.27060, 0.65328, 0.27060)
2025-06-01 03:41:32.2896 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testQuaternionIdentity, Type: Quaternion, Value: (0.00000, 0.00000, 0.00000, 1.00000)
2025-06-01 03:41:32.3002 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testColor, Type: Color, Value: RGBA(1.000, 0.000, 0.000, 1.000)
2025-06-01 03:41:32.3002 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testColorCustom, Type: Color, Value: RGBA(0.500, 0.300, 0.800, 0.900)
2025-06-01 03:41:32.3218 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testColor32, Type: Color32, Value: RGBA(255, 128, 64, 255)
2025-06-01 03:41:32.3312 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testRect, Type: Rect, Value: (x:10.00, y:20.00, width:100.00, height:200.00)
2025-06-01 03:41:32.3312 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testRectInt, Type: RectInt, Value: (x:5, y:10, width:50, height:100)
2025-06-01 03:41:32.3550 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testBounds, Type: Bounds, Value: Center: (0.00, 0.00, 0.00), Extents: (0.50, 0.50, 0.50)
2025-06-01 03:41:32.3650 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testBoundsInt, Type: BoundsInt, Value: Position: (0, 0, 0), Size: (10, 10, 10)
2025-06-01 03:41:32.3650 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testMatrix, Type: Matrix4x4, Value: 1.00000	0.00000	0.00000	0.00000
0.00000	1.00000	0.00000	0.00000
0.00000	0.00000	1.00000	0.00000
0.00... (类型: Matrix4x4)
2025-06-01 03:41:32.3832 [INFO] [StorageInstance]::Set(103) - Instance [Test_UnityTypes] - Key: testMatrixTRS, Type: Matrix4x4, Value: 2.00000	0.00000	0.00000	1.00000
0.00000	2.00000	0.00000	1.00000
0.00000	0.00000	2.00000	1.00000
0.00... (类型: Matrix4x4)
2025-06-01 03:41:32.3832 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 16 items
2025-06-01 03:41:32.3832 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_UnityTypes] - Starting sync save operation
2025-06-01 03:41:32.4349 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_UnityTypes.json
2025-06-01 03:41:32.4349 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_UnityTypes] - Data saved synchronously
2025-06-01 03:41:32.4349 [INFO] [StorageCache]::Clear(177) - Cleared 16 items from cache
2025-06-01 03:41:32.4349 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_UnityTypes
2025-06-01 03:41:32.4349 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_UnityTypes
2025-06-01 03:41:32.4924 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_CollectionTypes
2025-06-01 03:41:32.4924 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testIntArray, Type: Int32[], Value: [1, 2, 3, 4, 5]
2025-06-01 03:41:32.4924 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testFloatArray, Type: Single[], Value: [1.1, 2.2, 3.3]
2025-06-01 03:41:32.5025 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testStringArray, Type: String[], Value: ["apple", "banana", "cherry"]
2025-06-01 03:41:32.5025 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testVector3Array, Type: Vector3[], Value: [(0.00, 0.00, 0.00), (1.00, 1.00, 1.00), (0.00, 1.00, 0.00)]
2025-06-01 03:41:32.5025 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testEmptyIntArray, Type: Int32[], Value: []
2025-06-01 03:41:32.5235 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testIntList, Type: List`1, Value: [10, 20, 30, 40, 50]
2025-06-01 03:41:32.5235 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testStringList, Type: List`1, Value: ["Hello", "World", "Unity"]
2025-06-01 03:41:32.5380 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testVector3List, Type: List`1, Value: [(0.00, 0.00, 1.00), (0.00, 0.00, -1.00), (-1.00, 0.00, 0.00), (1.00, 0.00, 0.00)]
2025-06-01 03:41:32.5380 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testEmptyIntList, Type: List`1, Value: []
2025-06-01 03:41:32.5548 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testStringIntDict, Type: Dictionary`2, Value: {"first": 1, "second": 2, "third": 3}
2025-06-01 03:41:32.5548 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testIntStringDict, Type: Dictionary`2, Value: {1: "one", 2: "two", 3: "three"}
2025-06-01 03:41:32.5747 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testStringVector3Dict, Type: Dictionary`2, Value: {"origin": (0.00, 0.00, 0.00), "up": (0.00, 1.00, 0.00), "forward": (0.00, 0.00, 1.00)}
2025-06-01 03:41:32.5842 [INFO] [StorageInstance]::Set(103) - Instance [Test_CollectionTypes] - Key: testEmptyStringIntDict, Type: Dictionary`2, Value: {}
2025-06-01 03:41:32.5842 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 13 items
2025-06-01 03:41:32.5842 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_CollectionTypes] - Starting sync save operation
2025-06-01 03:41:32.6000 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_CollectionTypes.json
2025-06-01 03:41:32.6000 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_CollectionTypes] - Data saved synchronously
2025-06-01 03:41:32.6000 [INFO] [StorageCache]::Clear(177) - Cleared 13 items from cache
2025-06-01 03:41:32.6000 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_CollectionTypes
2025-06-01 03:41:32.6000 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_CollectionTypes
2025-06-01 03:41:32.6946 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_CustomTypes
2025-06-01 03:41:32.6946 [INFO] [StorageInstance]::Set(103) - Instance [Test_CustomTypes] - Key: testSimpleClass, Type: SimpleTestClass, Value: Storage.Test.StorageSerializerComprehensiveTest+SimpleTestClass
2025-06-01 03:41:32.6946 [INFO] [StorageInstance]::Set(103) - Instance [Test_CustomTypes] - Key: testStruct, Type: TestStruct, Value: Storage.Test.StorageSerializerComprehensiveTest+TestStruct
2025-06-01 03:41:32.7088 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 2 items
2025-06-01 03:41:32.7088 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_CustomTypes] - Starting sync save operation
2025-06-01 03:41:32.7088 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_CustomTypes.json
2025-06-01 03:41:32.7088 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_CustomTypes] - Data saved synchronously
2025-06-01 03:41:32.7088 [INFO] [StorageCache]::Clear(177) - Cleared 2 items from cache
2025-06-01 03:41:32.7088 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_CustomTypes
2025-06-01 03:41:32.7088 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_CustomTypes
2025-06-01 03:41:32.8104 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_NestedTypes
2025-06-01 03:41:32.8104 [INFO] [StorageInstance]::Set(103) - Instance [Test_NestedTypes] - Key: testComplexObj, Type: ComplexTestClass, Value: Storage.Test.StorageSerializerComprehensiveTest+ComplexTestClass
2025-06-01 03:41:32.8188 [INFO] [StorageInstance]::Set(103) - Instance [Test_NestedTypes] - Key: testNestedLists, Type: List`1, Value: [System.Collections.Generic.List`1[System.Int32], System.Collections.Generic.List`1[System.Int32], System.Collections.Generic.List`1[System.Int32]]
2025-06-01 03:41:32.8188 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 2 items
2025-06-01 03:41:32.8188 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_NestedTypes] - Starting sync save operation
2025-06-01 03:41:32.8372 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_NestedTypes.json
2025-06-01 03:41:32.8372 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_NestedTypes] - Data saved synchronously
2025-06-01 03:41:32.8372 [INFO] [StorageCache]::Clear(177) - Cleared 2 items from cache
2025-06-01 03:41:32.8372 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_NestedTypes
2025-06-01 03:41:32.8372 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_NestedTypes
2025-06-01 03:41:32.9277 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_NullValues
2025-06-01 03:41:32.9277 [INFO] [StorageInstance]::Set(103) - Instance [Test_NullValues] - Key: testNullString, Type: String, Value: null
2025-06-01 03:41:32.9277 [INFO] [StorageInstance]::Set(103) - Instance [Test_NullValues] - Key: testNullObject, Type: SimpleTestClass, Value: null
2025-06-01 03:41:32.9277 [INFO] [StorageInstance]::Set(103) - Instance [Test_NullValues] - Key: testNullArray, Type: Int32[], Value: null
2025-06-01 03:41:32.9435 [INFO] [StorageInstance]::Set(103) - Instance [Test_NullValues] - Key: testNullList, Type: List`1, Value: null
2025-06-01 03:41:32.9435 [INFO] [StorageInstance]::Set(103) - Instance [Test_NullValues] - Key: testArrayWithNulls, Type: String[], Value: ["first", null, "third", null]
2025-06-01 03:41:32.9435 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 5 items
2025-06-01 03:41:32.9554 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_NullValues] - Starting sync save operation
2025-06-01 03:41:32.9554 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_NullValues.json
2025-06-01 03:41:32.9554 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_NullValues] - Data saved synchronously
2025-06-01 03:41:32.9554 [INFO] [StorageCache]::Clear(177) - Cleared 5 items from cache
2025-06-01 03:41:32.9554 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_NullValues
2025-06-01 03:41:32.9554 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_NullValues
2025-06-01 03:41:33.0567 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_BoundaryConditions
2025-06-01 03:41:33.0689 [INFO] [StorageInstance]::Set(103) - Instance [Test_BoundaryConditions] - Key: testLargeList, Type: List`1, Value: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9... (共 10000 项)]
2025-06-01 03:41:33.0689 [INFO] [StorageInstance]::Set(103) - Instance [Test_BoundaryConditions] - Key: testLongString, Type: String, Value: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA... (长度: 10000)
2025-06-01 03:41:33.0689 [INFO] [StorageInstance]::Set(103) - Instance [Test_BoundaryConditions] - Key: testFloatNaN, Type: Single, Value: NaN
2025-06-01 03:41:33.0866 [INFO] [StorageInstance]::Set(103) - Instance [Test_BoundaryConditions] - Key: testFloatInfinity, Type: Single, Value: Infinity
2025-06-01 03:41:33.0993 [INFO] [StorageInstance]::Set(103) - Instance [Test_BoundaryConditions] - Key: testFloatNegInfinity, Type: Single, Value: -Infinity
2025-06-01 03:41:33.0993 [ERROR] [StorageInstance]::Set(96) - Key cannot be null or empty
2025-06-01 03:41:33.1124 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 5 items
2025-06-01 03:41:33.1124 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_BoundaryConditions] - Starting sync save operation
2025-06-01 03:41:33.1292 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_BoundaryConditions.json
2025-06-01 03:41:33.1292 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_BoundaryConditions] - Data saved synchronously
2025-06-01 03:41:33.1292 [INFO] [StorageCache]::Clear(177) - Cleared 5 items from cache
2025-06-01 03:41:33.1292 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_BoundaryConditions
2025-06-01 03:41:33.1292 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_BoundaryConditions
2025-06-01 03:41:33.2129 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_ErrorHandling
2025-06-01 03:41:33.2129 [WARN] [StorageCache]::TryGet(135) - Key 'nonExistentKey' not found
2025-06-01 03:41:33.2129 [INFO] [StorageInstance]::Set(103) - Instance [Test_ErrorHandling] - Key: typeMismatchTest, Type: Int32, Value: 42
2025-06-01 03:41:33.2245 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 1 items
2025-06-01 03:41:33.2245 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_ErrorHandling] - Starting sync save operation
2025-06-01 03:41:33.2245 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_ErrorHandling.json
2025-06-01 03:41:33.2245 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_ErrorHandling] - Data saved synchronously
2025-06-01 03:41:33.2245 [INFO] [StorageCache]::Clear(177) - Cleared 1 items from cache
2025-06-01 03:41:33.2245 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_ErrorHandling
2025-06-01 03:41:33.2245 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_ErrorHandling
2025-06-01 03:41:33.3250 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_PersistenceWrite
2025-06-01 03:41:33.3250 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistInt, Type: Object, Value: 12345
2025-06-01 03:41:33.3250 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistFloat, Type: Object, Value: 3.14159
2025-06-01 03:41:33.3342 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistString, Type: Object, Value: 持久化测试字符串
2025-06-01 03:41:33.3342 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistBool, Type: Object, Value: True
2025-06-01 03:41:33.3473 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistVector3, Type: Object, Value: (1.50, 2.50, 3.50)
2025-06-01 03:41:33.3473 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistColor, Type: Object, Value: RGBA(1.000, 0.000, 0.000, 1.000)
2025-06-01 03:41:33.3473 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistIntArray, Type: Object, Value: [1, 2, 3, 4, 5]
2025-06-01 03:41:33.3649 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistStringList, Type: Object, Value: ["apple", "banana", "cherry"]
2025-06-01 03:41:33.3649 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistDict, Type: Object, Value: [[one, 1], [two, 2], [three, 3]]
2025-06-01 03:41:33.3778 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 9 items
2025-06-01 03:41:33.3778 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_PersistenceWrite] - Starting sync save operation
2025-06-01 03:41:33.3778 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_PersistenceWrite.json
2025-06-01 03:41:33.3778 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_PersistenceWrite] - Data saved synchronously
2025-06-01 03:41:33.3778 [INFO] [StorageInstance]::SaveToFileSync(378) - Instance [Test_PersistenceWrite] - Cache is not dirty, skipping file save operation
2025-06-01 03:41:33.3778 [INFO] [StorageCache]::Clear(177) - Cleared 9 items from cache
2025-06-01 03:41:33.3778 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_PersistenceWrite
2025-06-01 03:41:33.3778 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_PersistenceWrite
2025-06-01 03:41:33.8799 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_PersistenceWrite
2025-06-01 03:41:33.8799 [INFO] [StorageInstance]::LoadFromFileSync(442) - Instance [Test_PersistenceWrite] - Starting sync load operation
2025-06-01 03:41:33.8951 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistInt' with type 'Int32' from inline format
2025-06-01 03:41:33.8951 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistFloat' with type 'Single' from inline format
2025-06-01 03:41:33.8951 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistString' with type 'String' from inline format
2025-06-01 03:41:33.8951 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistBool' with type 'Boolean' from inline format
2025-06-01 03:41:33.8951 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistVector3' with type 'Vector3' from inline format
2025-06-01 03:41:33.8951 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistColor' with type 'Color' from inline format
2025-06-01 03:41:33.8951 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistIntArray' with type 'Int32[]' from inline format
2025-06-01 03:41:33.8951 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistStringList' with type 'List`1' from inline format
2025-06-01 03:41:33.8951 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistDict' with type 'Dictionary`2' from inline format
2025-06-01 03:41:33.8951 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(88) - Loaded 9 items from inline metadata format
2025-06-01 03:41:33.8951 [INFO] [StorageCache]::LoadFromDictionary(211) - Loaded 9 items into cache from wrapper dictionary
2025-06-01 03:41:33.9083 [INFO] [StorageInstance]::LoadFromFileSync(458) - Instance [Test_PersistenceWrite] - Data loaded synchronously, 9 items
2025-06-01 03:41:33.9777 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistInt, Type: Int32, Value: 54321
2025-06-01 03:41:33.9777 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistString, Type: String, Value: 修改后的持久化字符串
2025-06-01 03:41:33.9777 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: newPersistKey, Type: String, Value: 新增的持久化数据
2025-06-01 03:41:33.9777 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 10 items
2025-06-01 03:41:33.9777 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_PersistenceWrite] - Starting sync save operation
2025-06-01 03:41:33.9777 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_PersistenceWrite.json
2025-06-01 03:41:33.9777 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_PersistenceWrite] - Data saved synchronously
2025-06-01 03:41:33.9777 [INFO] [StorageInstance]::SaveToFileSync(378) - Instance [Test_PersistenceWrite] - Cache is not dirty, skipping file save operation
2025-06-01 03:41:33.9777 [INFO] [StorageCache]::Clear(177) - Cleared 10 items from cache
2025-06-01 03:41:33.9777 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_PersistenceWrite
2025-06-01 03:41:33.9777 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_PersistenceWrite
2025-06-01 03:41:34.0796 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_AdvancedTypes
2025-06-01 03:41:34.0806 [INFO] [StorageInstance]::Set(103) - Instance [Test_AdvancedTypes] - Key: testInheritanceA, Type: Object, Value: Storage.Test.StorageSerializerComprehensiveTest+InheritanceTestA
2025-06-01 03:41:34.0806 [INFO] [StorageInstance]::Set(103) - Instance [Test_AdvancedTypes] - Key: testInheritanceB, Type: Object, Value: Storage.Test.StorageSerializerComprehensiveTest+InheritanceTestB
2025-06-01 03:41:34.0964 [INFO] [StorageInstance]::Set(103) - Instance [Test_AdvancedTypes] - Key: testGenericClass, Type: Object, Value: Storage.Test.StorageSerializerComprehensiveTest+GenericTestClass`1[System.Int32]
2025-06-01 03:41:34.0964 [INFO] [StorageInstance]::Set(103) - Instance [Test_AdvancedTypes] - Key: testDeepNested, Type: Object, Value: Storage.Test.StorageSerializerComprehensiveTest+DeepNestedTestClass
2025-06-01 03:41:34.1147 [INFO] [StorageInstance]::Set(103) - Instance [Test_AdvancedTypes] - Key: testMultiDimensional, Type: Object, Value: Storage.Test.StorageSerializerComprehensiveTest+MultiDimensionalTestClass
2025-06-01 03:41:34.1147 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 5 items
2025-06-01 03:41:34.1147 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_AdvancedTypes] - Starting sync save operation
2025-06-01 03:41:34.1438 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_AdvancedTypes.json
2025-06-01 03:41:34.1438 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_AdvancedTypes] - Data saved synchronously
2025-06-01 03:41:34.1438 [INFO] [StorageCache]::Clear(177) - Cleared 5 items from cache
2025-06-01 03:41:34.1438 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_AdvancedTypes
2025-06-01 03:41:34.1438 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_AdvancedTypes
2025-06-01 03:41:34.2278 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_EncryptionAES
2025-06-01 03:41:34.2278 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionAES] - Key: encryptedInt, Type: Object, Value: 999
2025-06-01 03:41:34.2278 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionAES] - Key: encryptedString, Type: Object, Value: 这是加密的字符串内容
2025-06-01 03:41:34.2385 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionAES] - Key: encryptedFloat, Type: Object, Value: 123.456
2025-06-01 03:41:34.2385 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionAES] - Key: encryptedBool, Type: Object, Value: True
2025-06-01 03:41:34.2567 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionAES] - Key: encryptedVector3, Type: Object, Value: (10.00, 20.00, 30.00)
2025-06-01 03:41:34.2567 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionAES] - Key: encryptedArray, Type: Object, Value: [10, 20, 30, 40, 50]
2025-06-01 03:41:34.2750 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionAES] - Key: encryptedList, Type: Object, Value: ["加密", "测试", "数据"]
2025-06-01 03:41:34.2837 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionAES] - Key: encryptedDict, Type: Object, Value: [[加密键1, 1.1], [加密键2, 2.2]]
2025-06-01 03:41:34.2837 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 8 items
2025-06-01 03:41:34.2837 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_EncryptionAES] - Starting sync save operation
2025-06-01 03:41:34.3003 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_EncryptionAES.json
2025-06-01 03:41:34.3003 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_EncryptionAES] - Data saved synchronously
2025-06-01 03:41:34.3889 [INFO] [StorageInstance]::SaveToFileSync(378) - Instance [Test_EncryptionAES] - Cache is not dirty, skipping file save operation
2025-06-01 03:41:34.3889 [INFO] [StorageCache]::Clear(177) - Cleared 8 items from cache
2025-06-01 03:41:34.3889 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_EncryptionAES
2025-06-01 03:41:34.3889 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_EncryptionAES
2025-06-01 03:41:34.4901 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_EncryptionXOR
2025-06-01 03:41:34.4901 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionXOR] - Key: encryptedInt, Type: Object, Value: 999
2025-06-01 03:41:34.4901 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionXOR] - Key: encryptedString, Type: Object, Value: 这是加密的字符串内容
2025-06-01 03:41:34.4901 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionXOR] - Key: encryptedFloat, Type: Object, Value: 123.456
2025-06-01 03:41:34.5063 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionXOR] - Key: encryptedBool, Type: Object, Value: True
2025-06-01 03:41:34.5063 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionXOR] - Key: encryptedVector3, Type: Object, Value: (10.00, 20.00, 30.00)
2025-06-01 03:41:34.5200 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionXOR] - Key: encryptedArray, Type: Object, Value: [10, 20, 30, 40, 50]
2025-06-01 03:41:34.5200 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionXOR] - Key: encryptedList, Type: Object, Value: ["加密", "测试", "数据"]
2025-06-01 03:41:34.5200 [INFO] [StorageInstance]::Set(103) - Instance [Test_EncryptionXOR] - Key: encryptedDict, Type: Object, Value: [[加密键1, 1.1], [加密键2, 2.2]]
2025-06-01 03:41:34.5370 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 8 items
2025-06-01 03:41:34.5370 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_EncryptionXOR] - Starting sync save operation
2025-06-01 03:41:34.5370 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_EncryptionXOR.json
2025-06-01 03:41:34.5370 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_EncryptionXOR] - Data saved synchronously
2025-06-01 03:41:34.5844 [INFO] [StorageInstance]::SaveToFileSync(378) - Instance [Test_EncryptionXOR] - Cache is not dirty, skipping file save operation
2025-06-01 03:41:34.5844 [INFO] [StorageCache]::Clear(177) - Cleared 8 items from cache
2025-06-01 03:41:34.5844 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_EncryptionXOR
2025-06-01 03:41:34.5844 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_EncryptionXOR
2025-06-01 03:41:49.0691 [INFO] [StorageManager]::OnApplicationFocusChanged(331) - Application lost focus, saving all storage instances...
2025-06-01 03:41:49.0691 [INFO] [StorageManager]::SaveAllInstances(283) - Save operation initiated for 0 instances
2025-06-01 03:45:51.4885 [INFO] [StorageManager]::OnApplicationFocusChanged(331) - Application lost focus, saving all storage instances...
2025-06-01 03:45:51.4885 [INFO] [StorageManager]::SaveAllInstances(283) - Save operation initiated for 0 instances
