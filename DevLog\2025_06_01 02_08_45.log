2025-06-01 02:08:45.9161 [INFO] [StorageTypeInitializer]::.cctor(326) - StorageTypeInitializer static initialization completed with 90 supported types
2025-06-01 02:08:45.9247 [INFO] [Storage]::InitializeCore(68) - Storage core initialized successfully with 90 supported types and 0 preloaded conversion delegates
2025-06-01 02:08:45.9247 [INFO] [StorageSettings]::InitializePathCache(71) - Storage paths cached successfully
2025-06-01 02:08:45.9247 [INFO] [StorageManager]::Initialize(88) - StorageManager initialized successfully
2025-06-01 02:08:46.4295 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_PersistenceWrite
2025-06-01 02:08:46.4403 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistInt, Type: Object, Value: 12345
2025-06-01 02:08:46.7649 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistFloat, Type: Object, Value: 3.14159
2025-06-01 02:08:46.8040 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistString, Type: Object, Value: 持久化测试字符串
2025-06-01 02:08:46.8584 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistBool, Type: Object, Value: True
2025-06-01 02:08:46.9186 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistVector3, Type: Object, Value: (1.50, 2.50, 3.50)
2025-06-01 02:08:46.9601 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistColor, Type: Object, Value: RGBA(1.000, 0.000, 0.000, 1.000)
2025-06-01 02:08:46.9769 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistIntArray, Type: Object, Value: [1, 2, 3, 4, 5]
2025-06-01 02:08:46.9922 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistStringList, Type: Object, Value: ["apple", "banana", "cherry"]
2025-06-01 02:08:47.0107 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistDict, Type: Object, Value: [[one, 1], [two, 2], [three, 3]]
2025-06-01 02:08:47.0278 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 9 items
2025-06-01 02:08:47.0278 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_PersistenceWrite] - Starting sync save operation
2025-06-01 02:08:47.0693 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_PersistenceWrite.json
2025-06-01 02:08:47.0693 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_PersistenceWrite] - Data saved synchronously
2025-06-01 02:08:47.0693 [INFO] [StorageInstance]::SaveToFileSync(378) - Instance [Test_PersistenceWrite] - Cache is not dirty, skipping file save operation
2025-06-01 02:08:47.0693 [INFO] [StorageCache]::Clear(177) - Cleared 9 items from cache
2025-06-01 02:08:47.0693 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_PersistenceWrite
2025-06-01 02:08:47.0693 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_PersistenceWrite
2025-06-01 02:08:47.5262 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_PersistenceWrite
2025-06-01 02:08:47.5262 [INFO] [StorageInstance]::LoadFromFileSync(442) - Instance [Test_PersistenceWrite] - Starting sync load operation
2025-06-01 02:08:47.5422 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistInt' with type 'Int32' from inline format
2025-06-01 02:08:47.5422 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistFloat' with type 'Single' from inline format
2025-06-01 02:08:47.5422 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistString' with type 'String' from inline format
2025-06-01 02:08:47.5422 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistBool' with type 'Boolean' from inline format
2025-06-01 02:08:47.5422 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistVector3' with type 'Vector3' from inline format
2025-06-01 02:08:47.5422 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistColor' with type 'Color' from inline format
2025-06-01 02:08:47.5563 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistIntArray' with type 'Int32[]' from inline format
2025-06-01 02:08:47.5563 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistStringList' with type 'List`1' from inline format
2025-06-01 02:08:47.5563 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistDict' with type 'Dictionary`2' from inline format
2025-06-01 02:08:47.5563 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(88) - Loaded 9 items from inline metadata format
2025-06-01 02:08:47.5563 [INFO] [StorageCache]::LoadFromDictionary(211) - Loaded 9 items into cache from wrapper dictionary
2025-06-01 02:08:47.5563 [INFO] [StorageInstance]::LoadFromFileSync(458) - Instance [Test_PersistenceWrite] - Data loaded synchronously, 9 items
2025-06-01 02:08:47.6141 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistInt, Type: Int32, Value: 54321
2025-06-01 02:08:47.6141 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistString, Type: String, Value: 修改后的持久化字符串
2025-06-01 02:08:47.6141 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: newPersistKey, Type: String, Value: 新增的持久化数据
2025-06-01 02:08:47.6141 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 10 items
2025-06-01 02:08:47.6141 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_PersistenceWrite] - Starting sync save operation
2025-06-01 02:08:47.6141 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_PersistenceWrite.json
2025-06-01 02:08:47.6141 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_PersistenceWrite] - Data saved synchronously
2025-06-01 02:08:47.6141 [INFO] [StorageInstance]::SaveToFileSync(378) - Instance [Test_PersistenceWrite] - Cache is not dirty, skipping file save operation
2025-06-01 02:08:47.6141 [INFO] [StorageCache]::Clear(177) - Cleared 10 items from cache
2025-06-01 02:08:47.6141 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_PersistenceWrite
2025-06-01 02:08:47.6141 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_PersistenceWrite
2025-06-01 02:08:49.1672 [INFO] [StorageManager]::OnApplicationFocusChanged(331) - Application lost focus, saving all storage instances...
2025-06-01 02:08:49.1672 [INFO] [StorageManager]::SaveAllInstances(283) - Save operation initiated for 0 instances
2025-06-01 02:08:49.2359 [INFO] [StorageManager]::OnApplicationQuitting(318) - Application quitting, saving all storage instances...
2025-06-01 02:08:49.2359 [INFO] [StorageManager]::SaveAllInstances(283) - Save operation initiated for 0 instances
