2025-06-01 02:06:51.5265 [INFO] [StorageTypeInitializer]::.cctor(326) - StorageTypeInitializer static initialization completed with 90 supported types
2025-06-01 02:06:51.5350 [INFO] [Storage]::InitializeCore(68) - Storage core initialized successfully with 90 supported types and 0 preloaded conversion delegates
2025-06-01 02:06:51.5350 [INFO] [StorageSettings]::InitializePathCache(71) - Storage paths cached successfully
2025-06-01 02:06:51.5350 [INFO] [StorageManager]::Initialize(88) - StorageManager initialized successfully
2025-06-01 02:06:52.0461 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_PersistenceWrite
2025-06-01 02:06:52.0551 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistInt, Type: Object, Value: 12345
2025-06-01 02:06:52.4175 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistFloat, Type: Object, Value: 3.14159
2025-06-01 02:06:52.4619 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistString, Type: Object, Value: 持久化测试字符串
2025-06-01 02:06:52.4882 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistBool, Type: Object, Value: True
2025-06-01 02:06:52.5168 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistVector3, Type: Object, Value: (1.50, 2.50, 3.50)
2025-06-01 02:06:52.5248 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistColor, Type: Object, Value: RGBA(1.000, 0.000, 0.000, 1.000)
2025-06-01 02:06:52.5339 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistIntArray, Type: Object, Value: [1, 2, 3, 4, 5]
2025-06-01 02:06:52.5339 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistStringList, Type: Object, Value: ["apple", "banana", "cherry"]
2025-06-01 02:06:52.5566 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistDict, Type: Object, Value: [[one, 1], [two, 2], [three, 3]]
2025-06-01 02:06:52.5657 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 9 items
2025-06-01 02:06:52.5657 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_PersistenceWrite] - Starting sync save operation
2025-06-01 02:06:52.6096 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_PersistenceWrite.json
2025-06-01 02:06:52.6096 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_PersistenceWrite] - Data saved synchronously
2025-06-01 02:06:52.6096 [INFO] [StorageInstance]::SaveToFileSync(378) - Instance [Test_PersistenceWrite] - Cache is not dirty, skipping file save operation
2025-06-01 02:06:52.6096 [INFO] [StorageCache]::Clear(177) - Cleared 9 items from cache
2025-06-01 02:06:52.6121 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_PersistenceWrite
2025-06-01 02:06:52.6121 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_PersistenceWrite
2025-06-01 02:06:53.0659 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: Test_PersistenceWrite
2025-06-01 02:06:53.0659 [INFO] [StorageInstance]::LoadFromFileSync(442) - Instance [Test_PersistenceWrite] - Starting sync load operation
2025-06-01 02:06:53.0659 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistInt' with type 'Int32' from inline format
2025-06-01 02:06:53.0806 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistFloat' with type 'Single' from inline format
2025-06-01 02:06:53.0806 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistString' with type 'String' from inline format
2025-06-01 02:06:53.0806 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistBool' with type 'Boolean' from inline format
2025-06-01 02:06:53.0806 [WARN] [StorageTypeMetadata]::ProcessValueForDeserialization(252) - StorageTypeConverter failed to convert value from type Dictionary`2 to Vector3
2025-06-01 02:06:53.0806 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistVector3' with type 'Vector3' from inline format
2025-06-01 02:06:53.0806 [WARN] [StorageTypeMetadata]::ProcessValueForDeserialization(252) - StorageTypeConverter failed to convert value from type Dictionary`2 to Color
2025-06-01 02:06:53.0806 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistColor' with type 'Color' from inline format
2025-06-01 02:06:53.0806 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistIntArray' with type 'Int32[]' from inline format
2025-06-01 02:06:53.0806 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistStringList' with type 'List`1' from inline format
2025-06-01 02:06:53.0806 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(74) - Loaded key 'persistDict' with type 'Dictionary`2' from inline format
2025-06-01 02:06:53.0806 [INFO] [StorageTypeMetadata]::LoadWithInlineMetadata(88) - Loaded 9 items from inline metadata format
2025-06-01 02:06:53.0806 [INFO] [StorageCache]::LoadFromDictionary(211) - Loaded 9 items into cache from wrapper dictionary
2025-06-01 02:06:53.0806 [INFO] [StorageInstance]::LoadFromFileSync(458) - Instance [Test_PersistenceWrite] - Data loaded synchronously, 9 items
2025-06-01 02:06:53.1585 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistInt, Type: Int32, Value: 54321
2025-06-01 02:06:53.1585 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: persistString, Type: String, Value: 修改后的持久化字符串
2025-06-01 02:06:53.1585 [INFO] [StorageInstance]::Set(103) - Instance [Test_PersistenceWrite] - Key: newPersistKey, Type: String, Value: 新增的持久化数据
2025-06-01 02:06:53.1585 [INFO] [StorageTypeMetadata]::SaveWithInlineMetadata(48) - Generated inline metadata for 10 items
2025-06-01 02:06:53.1585 [INFO] [StorageInstance]::SaveToFileSync(388) - Instance [Test_PersistenceWrite] - Starting sync save operation
2025-06-01 02:06:53.1585 [INFO] [Storage]::WriteToFileWithBackup(261) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\Test_PersistenceWrite.json
2025-06-01 02:06:53.1585 [INFO] [StorageInstance]::SaveToFileSync(410) - Instance [Test_PersistenceWrite] - Data saved synchronously
2025-06-01 02:06:53.1585 [INFO] [StorageInstance]::SaveToFileSync(378) - Instance [Test_PersistenceWrite] - Cache is not dirty, skipping file save operation
2025-06-01 02:06:53.1585 [INFO] [StorageCache]::Clear(177) - Cleared 10 items from cache
2025-06-01 02:06:53.1585 [INFO] [StorageInstance]::Dispose(1093) - Storage instance disposed: Test_PersistenceWrite
2025-06-01 02:06:53.1585 [INFO] [StorageManager]::DestroyInstance(217) - Storage instance destroyed: Test_PersistenceWrite
2025-06-01 02:06:55.4257 [INFO] [StorageManager]::OnApplicationFocusChanged(331) - Application lost focus, saving all storage instances...
2025-06-01 02:06:55.4257 [INFO] [StorageManager]::SaveAllInstances(283) - Save operation initiated for 0 instances
2025-06-01 02:06:56.7081 [INFO] [StorageManager]::OnApplicationFocusChanged(331) - Application lost focus, saving all storage instances...
2025-06-01 02:06:56.7081 [INFO] [StorageManager]::SaveAllInstances(283) - Save operation initiated for 0 instances
2025-06-01 02:07:56.5757 [INFO] [StorageManager]::OnApplicationFocusChanged(331) - Application lost focus, saving all storage instances...
2025-06-01 02:07:56.5757 [INFO] [StorageManager]::SaveAllInstances(283) - Save operation initiated for 0 instances
2025-06-01 02:07:56.7167 [INFO] [StorageManager]::OnApplicationQuitting(318) - Application quitting, saving all storage instances...
2025-06-01 02:07:56.7167 [INFO] [StorageManager]::SaveAllInstances(283) - Save operation initiated for 0 instances
