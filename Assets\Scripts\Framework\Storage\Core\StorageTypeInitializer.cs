using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Runtime.CompilerServices;
using UnityEngine;
using DGame.Framework;

namespace Storage
{
    /// <summary>
    /// 存储类型初始化管理器
    /// 统一管理所有存储系统支持的类型，包括类型标识符映射、类型工厂和转换委托的初始化
    /// 优化性能：预加载所有支持的类型转换委托，避免运行时生成
    /// </summary>
    public static class StorageTypeInitializer
    {
        #region 私有字段

        // 所有支持的类型列表
        private static readonly Type[] _supportedTypes;

        // 类型标识符映射
        private static readonly Dictionary<Type, string> _typeToIdentifier;
        private static readonly Dictionary<string, Type> _identifierToType;

        /// <summary>
        /// 工厂函数字典
        /// </summary>
        private static Dictionary<Type, Func<StorageTypeWrapper>> _typeFactories;

        // 类型转换委托缓存
        private static readonly ConcurrentDictionary<StorageTypeConverter.DeserializationDelagateKey, StorageTypeConverter.DeserializationTypeConvertDelegate> _converReflectionDelegates;

        // 类型反射工厂映射
        private static readonly ConcurrentDictionary<Type, Func<StorageTypeWrapper>> _typeReflectionFactories;

        // 表达式树缓存 - 集合操作委托
        private static readonly ConcurrentDictionary<Type, Func<object, int>> _listCountGetters;
        private static readonly ConcurrentDictionary<Type, Func<object, int, object>> _listItemGetters;
        private static readonly ConcurrentDictionary<Type, Action<object, object>> _listAddMethods;
        private static readonly ConcurrentDictionary<Type, Func<object, System.Collections.IEnumerable>> _dictKeyGetters;
        private static readonly ConcurrentDictionary<Type, Func<object, object, object>> _dictItemGetters;
        private static readonly ConcurrentDictionary<Type, Action<object, object, object>> _dictAddMethods;

        #endregion

        #region 静态构造函数

        private static Dictionary<Type, Func<StorageTypeWrapper>> CreateTypeFactories()
        {
            var factories = new Dictionary<Type, Func<StorageTypeWrapper>>()
            {
                // 注册基础类型 - 使用静态方法避免闭包
                {typeof(int), StorageCollectionDelegates.CreateIntWrapper},
                {typeof(float), StorageCollectionDelegates.CreateFloatWrapper},
                {typeof(bool), StorageCollectionDelegates.CreateBoolWrapper},
                {typeof(long), StorageCollectionDelegates.CreateLongWrapper},
                {typeof(double), StorageCollectionDelegates.CreateDoubleWrapper},
                {typeof(string), StorageCollectionDelegates.CreateStringWrapper},
                {typeof(byte), StorageCollectionDelegates.CreateByteWrapper},
                {typeof(sbyte), StorageCollectionDelegates.CreateSByteWrapper},
                {typeof(short), StorageCollectionDelegates.CreateShortWrapper},
                {typeof(ushort), StorageCollectionDelegates.CreateUShortWrapper},
                {typeof(uint), StorageCollectionDelegates.CreateUIntWrapper},
                {typeof(ulong), StorageCollectionDelegates.CreateULongWrapper},
                {typeof(decimal), StorageCollectionDelegates.CreateDecimalWrapper},
                {typeof(char), StorageCollectionDelegates.CreateCharWrapper},
                {typeof(DateTime), StorageCollectionDelegates.CreateDateTimeWrapper},
                {typeof(Guid), StorageCollectionDelegates.CreateGuidWrapper},

                // 注册Unity类型 - 使用静态方法避免闭包
                {typeof(Vector2), StorageCollectionDelegates.CreateVector2Wrapper},
                {typeof(Vector3), StorageCollectionDelegates.CreateVector3Wrapper},
                {typeof(Vector4), StorageCollectionDelegates.CreateVector4Wrapper},
                {typeof(Quaternion), StorageCollectionDelegates.CreateQuaternionWrapper},
                {typeof(Color), StorageCollectionDelegates.CreateColorWrapper},
                {typeof(Rect), StorageCollectionDelegates.CreateRectWrapper},
                {typeof(RectInt), StorageCollectionDelegates.CreateRectIntWrapper},
                {typeof(Bounds), StorageCollectionDelegates.CreateBoundsWrapper},
                {typeof(BoundsInt), StorageCollectionDelegates.CreateBoundsIntWrapper},
                {typeof(Vector2Int), StorageCollectionDelegates.CreateVector2IntWrapper},
                {typeof(Vector3Int), StorageCollectionDelegates.CreateVector3IntWrapper},
                {typeof(Color32), StorageCollectionDelegates.CreateColor32Wrapper},
                {typeof(Matrix4x4), StorageCollectionDelegates.CreateMatrix4x4Wrapper},
                {typeof(LayerMask), StorageCollectionDelegates.CreateLayerMaskWrapper},

                // 注册数组类型 - 使用静态方法避免闭包
                {typeof(int[]), StorageCollectionDelegates.CreateIntArrayWrapper},
                {typeof(float[]), StorageCollectionDelegates.CreateFloatArrayWrapper},
                {typeof(double[]), StorageCollectionDelegates.CreateDoubleArrayWrapper},
                {typeof(bool[]), StorageCollectionDelegates.CreateBoolArrayWrapper},
                {typeof(string[]), StorageCollectionDelegates.CreateStringArrayWrapper},
                {typeof(long[]), StorageCollectionDelegates.CreateLongArrayWrapper},
                {typeof(char[]), StorageCollectionDelegates.CreateCharArrayWrapper},
                {typeof(byte[]), StorageCollectionDelegates.CreateByteArrayWrapper},
                {typeof(sbyte[]), StorageCollectionDelegates.CreateSByteArrayWrapper},
                {typeof(short[]), StorageCollectionDelegates.CreateShortArrayWrapper},
                {typeof(ushort[]), StorageCollectionDelegates.CreateUShortArrayWrapper},
                {typeof(uint[]), StorageCollectionDelegates.CreateUIntArrayWrapper},
                {typeof(ulong[]), StorageCollectionDelegates.CreateULongArrayWrapper},
                {typeof(decimal[]), StorageCollectionDelegates.CreateDecimalArrayWrapper},
                {typeof(Vector2[]), StorageCollectionDelegates.CreateVector2ArrayWrapper},
                {typeof(Vector3[]), StorageCollectionDelegates.CreateVector3ArrayWrapper},
                {typeof(Vector4[]), StorageCollectionDelegates.CreateVector4ArrayWrapper},
                {typeof(Quaternion[]), StorageCollectionDelegates.CreateQuaternionArrayWrapper},
                {typeof(Color[]), StorageCollectionDelegates.CreateColorArrayWrapper},
                {typeof(Color32[]), StorageCollectionDelegates.CreateColor32ArrayWrapper},
                {typeof(Rect[]), StorageCollectionDelegates.CreateRectArrayWrapper},

                // List 集合类型 - 使用静态方法避免闭包
                {typeof(List<int>), StorageCollectionDelegates.CreateListIntWrapper},
                {typeof(List<float>), StorageCollectionDelegates.CreateListFloatWrapper},
                {typeof(List<double>), StorageCollectionDelegates.CreateListDoubleWrapper},
                {typeof(List<bool>), StorageCollectionDelegates.CreateListBoolWrapper},
                {typeof(List<string>), StorageCollectionDelegates.CreateListStringWrapper},
                {typeof(List<long>), StorageCollectionDelegates.CreateListLongWrapper},
                {typeof(List<char>), StorageCollectionDelegates.CreateListCharWrapper},
                {typeof(List<byte>), StorageCollectionDelegates.CreateListByteWrapper},
                {typeof(List<sbyte>), StorageCollectionDelegates.CreateListSByteWrapper},
                {typeof(List<short>), StorageCollectionDelegates.CreateListShortWrapper},
                {typeof(List<ushort>), StorageCollectionDelegates.CreateListUShortWrapper},
                {typeof(List<uint>), StorageCollectionDelegates.CreateListUIntWrapper},
                {typeof(List<ulong>), StorageCollectionDelegates.CreateListULongWrapper},
                {typeof(List<decimal>), StorageCollectionDelegates.CreateListDecimalWrapper},
                {typeof(List<Vector2>), StorageCollectionDelegates.CreateListVector2Wrapper},
                {typeof(List<Vector3>), StorageCollectionDelegates.CreateListVector3Wrapper},
                {typeof(List<Vector4>), StorageCollectionDelegates.CreateListVector4Wrapper},
                {typeof(List<Quaternion>), StorageCollectionDelegates.CreateListQuaternionWrapper},
                {typeof(List<Color>), StorageCollectionDelegates.CreateListColorWrapper},
                {typeof(List<Color32>), StorageCollectionDelegates.CreateListColor32Wrapper},

                // Dictionary 集合类型 (string键) - 使用静态方法避免闭包
                {typeof(Dictionary<string, int>), StorageCollectionDelegates.CreateDictStringIntWrapper},
                {typeof(Dictionary<string, float>), StorageCollectionDelegates.CreateDictStringFloatWrapper},
                {typeof(Dictionary<string, double>), StorageCollectionDelegates.CreateDictStringDoubleWrapper},
                {typeof(Dictionary<string, bool>), StorageCollectionDelegates.CreateDictStringBoolWrapper},
                {typeof(Dictionary<string, string>), StorageCollectionDelegates.CreateDictStringStringWrapper},
                {typeof(Dictionary<string, long>), StorageCollectionDelegates.CreateDictStringLongWrapper},
                {typeof(Dictionary<string, Vector2>), StorageCollectionDelegates.CreateDictStringVector2Wrapper},
                {typeof(Dictionary<string, Vector3>), StorageCollectionDelegates.CreateDictStringVector3Wrapper},
                {typeof(Dictionary<string, Vector4>), StorageCollectionDelegates.CreateDictStringVector4Wrapper},
                {typeof(Dictionary<string, Quaternion>), StorageCollectionDelegates.CreateDictStringQuaternionWrapper},
                {typeof(Dictionary<string, Color>), StorageCollectionDelegates.CreateDictStringColorWrapper},
                {typeof(Dictionary<string, Color32>), StorageCollectionDelegates.CreateDictStringColor32Wrapper},

                // Dictionary 集合类型 (int键) - 使用静态方法避免闭包
                {typeof(Dictionary<int, int>), StorageCollectionDelegates.CreateDictIntIntWrapper},
                {typeof(Dictionary<int, float>), StorageCollectionDelegates.CreateDictIntFloatWrapper},
                {typeof(Dictionary<int, double>), StorageCollectionDelegates.CreateDictIntDoubleWrapper},
                {typeof(Dictionary<int, bool>), StorageCollectionDelegates.CreateDictIntBoolWrapper},
                {typeof(Dictionary<int, string>), StorageCollectionDelegates.CreateDictIntStringWrapper},
                {typeof(Dictionary<int, Vector2>), StorageCollectionDelegates.CreateDictIntVector2Wrapper},
                {typeof(Dictionary<int, Vector3>), StorageCollectionDelegates.CreateDictIntVector3Wrapper},
                {typeof(Dictionary<int, Color>), StorageCollectionDelegates.CreateDictIntColorWrapper},
            };

            return factories;
        }

        /// <summary>
        /// 创建类型标识符映射
        /// </summary>
        /// <returns>类型到标识符的映射字典</returns>
        private static Dictionary<Type, string> CreateTypeIdentifierMapping()
        {
            var mapping = new Dictionary<Type, string>();

            // 基础类型（单字符标识符）
            mapping[typeof(int)] = "i";
            mapping[typeof(float)] = "f";
            mapping[typeof(double)] = "d";
            mapping[typeof(bool)] = "b";
            mapping[typeof(string)] = "s";
            mapping[typeof(long)] = "l";
            mapping[typeof(char)] = "c";
            mapping[typeof(byte)] = "y";
            mapping[typeof(sbyte)] = "Y";
            mapping[typeof(short)] = "h";
            mapping[typeof(ushort)] = "H";
            mapping[typeof(uint)] = "u";
            mapping[typeof(ulong)] = "U";
            mapping[typeof(decimal)] = "m";
            mapping[typeof(DateTime)] = "dt";
            mapping[typeof(Guid)] = "g";

            // Unity基础类型
            mapping[typeof(Vector2)] = "V2";
            mapping[typeof(Vector3)] = "V3";
            mapping[typeof(Vector4)] = "V4";
            mapping[typeof(Quaternion)] = "Q";
            mapping[typeof(Color)] = "C";
            mapping[typeof(Color32)] = "C32";
            mapping[typeof(Rect)] = "R";
            mapping[typeof(Bounds)] = "B";
            mapping[typeof(Matrix4x4)] = "M4";
            mapping[typeof(Vector2Int)] = "V2i";
            mapping[typeof(Vector3Int)] = "V3i";
            mapping[typeof(RectInt)] = "Ri";
            mapping[typeof(BoundsInt)] = "Bi";
            mapping[typeof(LayerMask)] = "LM";

            // 数组类型
            mapping[typeof(int[])] = "i[]";
            mapping[typeof(float[])] = "f[]";
            mapping[typeof(double[])] = "d[]";
            mapping[typeof(bool[])] = "b[]";
            mapping[typeof(string[])] = "s[]";
            mapping[typeof(long[])] = "l[]";
            mapping[typeof(char[])] = "c[]";
            mapping[typeof(byte[])] = "y[]";
            mapping[typeof(sbyte[])] = "Y[]";
            mapping[typeof(short[])] = "h[]";
            mapping[typeof(ushort[])] = "H[]";
            mapping[typeof(uint[])] = "u[]";
            mapping[typeof(ulong[])] = "U[]";
            mapping[typeof(decimal[])] = "m[]";
            mapping[typeof(Vector2[])] = "V2[]";
            mapping[typeof(Vector3[])] = "V3[]";
            mapping[typeof(Vector4[])] = "V4[]";
            mapping[typeof(Quaternion[])] = "Q[]";
            mapping[typeof(Color[])] = "C[]";
            mapping[typeof(Color32[])] = "C32[]";

            // List 集合类型
            mapping[typeof(List<int>)] = "Li";
            mapping[typeof(List<float>)] = "Lf";
            mapping[typeof(List<double>)] = "Ld";
            mapping[typeof(List<bool>)] = "Lb";
            mapping[typeof(List<string>)] = "Ls";
            mapping[typeof(List<long>)] = "Ll";
            mapping[typeof(List<char>)] = "Lc";
            mapping[typeof(List<byte>)] = "Ly";
            mapping[typeof(List<sbyte>)] = "LY";
            mapping[typeof(List<short>)] = "Lh";
            mapping[typeof(List<ushort>)] = "LH";
            mapping[typeof(List<uint>)] = "Lu";
            mapping[typeof(List<ulong>)] = "LU";
            mapping[typeof(List<decimal>)] = "Lm";
            mapping[typeof(List<Vector2>)] = "LV2";
            mapping[typeof(List<Vector3>)] = "LV3";
            mapping[typeof(List<Vector4>)] = "LV4";
            mapping[typeof(List<Quaternion>)] = "LQ";
            mapping[typeof(List<Color>)] = "LC";
            mapping[typeof(List<Color32>)] = "LC32";

            // Dictionary 集合类型 (string键)
            mapping[typeof(Dictionary<string, int>)] = "Dsi";
            mapping[typeof(Dictionary<string, float>)] = "Dsf";
            mapping[typeof(Dictionary<string, double>)] = "Dsd";
            mapping[typeof(Dictionary<string, bool>)] = "Dsb";
            mapping[typeof(Dictionary<string, string>)] = "Dss";
            mapping[typeof(Dictionary<string, long>)] = "Dsl";
            mapping[typeof(Dictionary<string, Vector2>)] = "DsV2";
            mapping[typeof(Dictionary<string, Vector3>)] = "DsV3";
            mapping[typeof(Dictionary<string, Vector4>)] = "DsV4";
            mapping[typeof(Dictionary<string, Quaternion>)] = "DsQ";
            mapping[typeof(Dictionary<string, Color>)] = "DsC";
            mapping[typeof(Dictionary<string, Color32>)] = "DsC32";

            // Dictionary 集合类型 (int键)
            mapping[typeof(Dictionary<int, int>)] = "Dii";
            mapping[typeof(Dictionary<int, float>)] = "Dif";
            mapping[typeof(Dictionary<int, double>)] = "Did";
            mapping[typeof(Dictionary<int, bool>)] = "Dib";
            mapping[typeof(Dictionary<int, string>)] = "Dis";
            mapping[typeof(Dictionary<int, Vector2>)] = "DiV2";
            mapping[typeof(Dictionary<int, Vector3>)] = "DiV3";
            mapping[typeof(Dictionary<int, Color>)] = "DiC";

            return mapping;
        }

        /// <summary>
        /// 预缓存常见集合类型的强类型委托（避免表达式树编译开销）
        /// </summary>
        private static void PreCacheCommonCollectionTypes()
        {
            int tmpCachedCount = 0;
            // 预缓存 List 类型的强类型委托
            tmpCachedCount += PreCacheListDelegates();
            // 预缓存 Dictionary 类型的强类型委托
            tmpCachedCount += PreCacheDictionaryDelegates();
            NLogger.Log("Pre-cached {0} collection operation delegates for common types", arg0: tmpCachedCount);
        }

        /// <summary>
        /// 预缓存 List 类型的静态方法委托（避免闭包和内存分配）
        /// </summary>
        /// <returns>缓存的委托数量</returns>
        private static int PreCacheListDelegates()
        {
            int count = 0;

            // List<int> - 使用静态方法避免闭包
            _listCountGetters.TryAdd(typeof(List<int>), StorageCollectionDelegates.GetListIntCount);
            _listItemGetters.TryAdd(typeof(List<int>), StorageCollectionDelegates.GetListIntItem);
            _listAddMethods.TryAdd(typeof(List<int>), StorageCollectionDelegates.AddToListInt);
            count += 3;

            // List<float>
            _listCountGetters.TryAdd(typeof(List<float>), StorageCollectionDelegates.GetListFloatCount);
            _listItemGetters.TryAdd(typeof(List<float>), StorageCollectionDelegates.GetListFloatItem);
            _listAddMethods.TryAdd(typeof(List<float>), StorageCollectionDelegates.AddToListFloat);
            count += 3;

            // List<double>
            _listCountGetters.TryAdd(typeof(List<double>), StorageCollectionDelegates.GetListDoubleCount);
            _listItemGetters.TryAdd(typeof(List<double>), StorageCollectionDelegates.GetListDoubleItem);
            _listAddMethods.TryAdd(typeof(List<double>), StorageCollectionDelegates.AddToListDouble);
            count += 3;

            // List<bool>
            _listCountGetters.TryAdd(typeof(List<bool>), StorageCollectionDelegates.GetListBoolCount);
            _listItemGetters.TryAdd(typeof(List<bool>), StorageCollectionDelegates.GetListBoolItem);
            _listAddMethods.TryAdd(typeof(List<bool>), StorageCollectionDelegates.AddToListBool);
            count += 3;

            // List<string>
            _listCountGetters.TryAdd(typeof(List<string>), StorageCollectionDelegates.GetListStringCount);
            _listItemGetters.TryAdd(typeof(List<string>), StorageCollectionDelegates.GetListStringItem);
            _listAddMethods.TryAdd(typeof(List<string>), StorageCollectionDelegates.AddToListString);
            count += 3;

            // List<long>
            _listCountGetters.TryAdd(typeof(List<long>), StorageCollectionDelegates.GetListLongCount);
            _listItemGetters.TryAdd(typeof(List<long>), StorageCollectionDelegates.GetListLongItem);
            _listAddMethods.TryAdd(typeof(List<long>), StorageCollectionDelegates.AddToListLong);
            count += 3;

            // List<Vector2>
            _listCountGetters.TryAdd(typeof(List<Vector2>), StorageCollectionDelegates.GetListVector2Count);
            _listItemGetters.TryAdd(typeof(List<Vector2>), StorageCollectionDelegates.GetListVector2Item);
            _listAddMethods.TryAdd(typeof(List<Vector2>), StorageCollectionDelegates.AddToListVector2);
            count += 3;

            // List<Vector3>
            _listCountGetters.TryAdd(typeof(List<Vector3>), StorageCollectionDelegates.GetListVector3Count);
            _listItemGetters.TryAdd(typeof(List<Vector3>), StorageCollectionDelegates.GetListVector3Item);
            _listAddMethods.TryAdd(typeof(List<Vector3>), StorageCollectionDelegates.AddToListVector3);
            count += 3;

            // List<Color>
            _listCountGetters.TryAdd(typeof(List<Color>), StorageCollectionDelegates.GetListColorCount);
            _listItemGetters.TryAdd(typeof(List<Color>), StorageCollectionDelegates.GetListColorItem);
            _listAddMethods.TryAdd(typeof(List<Color>), StorageCollectionDelegates.AddToListColor);
            count += 3;

            // List<byte>
            _listCountGetters.TryAdd(typeof(List<byte>), StorageCollectionDelegates.GetListByteCount);
            _listItemGetters.TryAdd(typeof(List<byte>), StorageCollectionDelegates.GetListByteItem);
            _listAddMethods.TryAdd(typeof(List<byte>), StorageCollectionDelegates.AddToListByte);
            count += 3;

            // List<sbyte>
            _listCountGetters.TryAdd(typeof(List<sbyte>), StorageCollectionDelegates.GetListSByteCount);
            _listItemGetters.TryAdd(typeof(List<sbyte>), StorageCollectionDelegates.GetListSByteItem);
            _listAddMethods.TryAdd(typeof(List<sbyte>), StorageCollectionDelegates.AddToListSByte);
            count += 3;

            // List<short>
            _listCountGetters.TryAdd(typeof(List<short>), StorageCollectionDelegates.GetListShortCount);
            _listItemGetters.TryAdd(typeof(List<short>), StorageCollectionDelegates.GetListShortItem);
            _listAddMethods.TryAdd(typeof(List<short>), StorageCollectionDelegates.AddToListShort);
            count += 3;

            // List<ushort>
            _listCountGetters.TryAdd(typeof(List<ushort>), StorageCollectionDelegates.GetListUShortCount);
            _listItemGetters.TryAdd(typeof(List<ushort>), StorageCollectionDelegates.GetListUShortItem);
            _listAddMethods.TryAdd(typeof(List<ushort>), StorageCollectionDelegates.AddToListUShort);
            count += 3;

            // List<uint>
            _listCountGetters.TryAdd(typeof(List<uint>), StorageCollectionDelegates.GetListUIntCount);
            _listItemGetters.TryAdd(typeof(List<uint>), StorageCollectionDelegates.GetListUIntItem);
            _listAddMethods.TryAdd(typeof(List<uint>), StorageCollectionDelegates.AddToListUInt);
            count += 3;

            // List<ulong>
            _listCountGetters.TryAdd(typeof(List<ulong>), StorageCollectionDelegates.GetListULongCount);
            _listItemGetters.TryAdd(typeof(List<ulong>), StorageCollectionDelegates.GetListULongItem);
            _listAddMethods.TryAdd(typeof(List<ulong>), StorageCollectionDelegates.AddToListULong);
            count += 3;

            // List<decimal>
            _listCountGetters.TryAdd(typeof(List<decimal>), StorageCollectionDelegates.GetListDecimalCount);
            _listItemGetters.TryAdd(typeof(List<decimal>), StorageCollectionDelegates.GetListDecimalItem);
            _listAddMethods.TryAdd(typeof(List<decimal>), StorageCollectionDelegates.AddToListDecimal);
            count += 3;

            // List<Vector4>
            _listCountGetters.TryAdd(typeof(List<Vector4>), StorageCollectionDelegates.GetListVector4Count);
            _listItemGetters.TryAdd(typeof(List<Vector4>), StorageCollectionDelegates.GetListVector4Item);
            _listAddMethods.TryAdd(typeof(List<Vector4>), StorageCollectionDelegates.AddToListVector4);
            count += 3;

            // List<Quaternion>
            _listCountGetters.TryAdd(typeof(List<Quaternion>), StorageCollectionDelegates.GetListQuaternionCount);
            _listItemGetters.TryAdd(typeof(List<Quaternion>), StorageCollectionDelegates.GetListQuaternionItem);
            _listAddMethods.TryAdd(typeof(List<Quaternion>), StorageCollectionDelegates.AddToListQuaternion);
            count += 3;

            // List<Color32>
            _listCountGetters.TryAdd(typeof(List<Color32>), StorageCollectionDelegates.GetListColor32Count);
            _listItemGetters.TryAdd(typeof(List<Color32>), StorageCollectionDelegates.GetListColor32Item);
            _listAddMethods.TryAdd(typeof(List<Color32>), StorageCollectionDelegates.AddToListColor32);
            count += 3;

            return count;
        }

        /// <summary>
        /// 预缓存 Dictionary 类型的静态方法委托（避免闭包和内存分配）
        /// </summary>
        /// <returns>缓存的委托数量</returns>
        private static int PreCacheDictionaryDelegates()
        {
            int count = 0;

            // Dictionary<string, int> - 使用静态方法避免闭包
            _dictKeyGetters.TryAdd(typeof(Dictionary<string, int>), StorageCollectionDelegates.GetDictStringIntKeys);
            _dictItemGetters.TryAdd(typeof(Dictionary<string, int>), StorageCollectionDelegates.GetDictStringIntItem);
            _dictAddMethods.TryAdd(typeof(Dictionary<string, int>), StorageCollectionDelegates.AddToDictStringInt);
            count += 3;

            // Dictionary<string, float>
            _dictKeyGetters.TryAdd(typeof(Dictionary<string, float>), StorageCollectionDelegates.GetDictStringFloatKeys);
            _dictItemGetters.TryAdd(typeof(Dictionary<string, float>), StorageCollectionDelegates.GetDictStringFloatItem);
            _dictAddMethods.TryAdd(typeof(Dictionary<string, float>), StorageCollectionDelegates.AddToDictStringFloat);
            count += 3;

            // Dictionary<string, string>
            _dictKeyGetters.TryAdd(typeof(Dictionary<string, string>), StorageCollectionDelegates.GetDictStringStringKeys);
            _dictItemGetters.TryAdd(typeof(Dictionary<string, string>), StorageCollectionDelegates.GetDictStringStringItem);
            _dictAddMethods.TryAdd(typeof(Dictionary<string, string>), StorageCollectionDelegates.AddToDictStringString);
            count += 3;

            // Dictionary<string, Vector3>
            _dictKeyGetters.TryAdd(typeof(Dictionary<string, Vector3>), StorageCollectionDelegates.GetDictStringVector3Keys);
            _dictItemGetters.TryAdd(typeof(Dictionary<string, Vector3>), StorageCollectionDelegates.GetDictStringVector3Item);
            _dictAddMethods.TryAdd(typeof(Dictionary<string, Vector3>), StorageCollectionDelegates.AddToDictStringVector3);
            count += 3;

            // Dictionary<string, Color>
            _dictKeyGetters.TryAdd(typeof(Dictionary<string, Color>), StorageCollectionDelegates.GetDictStringColorKeys);
            _dictItemGetters.TryAdd(typeof(Dictionary<string, Color>), StorageCollectionDelegates.GetDictStringColorItem);
            _dictAddMethods.TryAdd(typeof(Dictionary<string, Color>), StorageCollectionDelegates.AddToDictStringColor);
            count += 3;

            // Dictionary<int, int>
            _dictKeyGetters.TryAdd(typeof(Dictionary<int, int>), StorageCollectionDelegates.GetDictIntIntKeys);
            _dictItemGetters.TryAdd(typeof(Dictionary<int, int>), StorageCollectionDelegates.GetDictIntIntItem);
            _dictAddMethods.TryAdd(typeof(Dictionary<int, int>), StorageCollectionDelegates.AddToDictIntInt);
            count += 3;

            // Dictionary<int, string>
            _dictKeyGetters.TryAdd(typeof(Dictionary<int, string>), StorageCollectionDelegates.GetDictIntStringKeys);
            _dictItemGetters.TryAdd(typeof(Dictionary<int, string>), StorageCollectionDelegates.GetDictIntStringItem);
            _dictAddMethods.TryAdd(typeof(Dictionary<int, string>), StorageCollectionDelegates.AddToDictIntString);
            count += 3;

            // Dictionary<string, double>
            _dictKeyGetters.TryAdd(typeof(Dictionary<string, double>), StorageCollectionDelegates.GetDictStringDoubleKeys);
            _dictItemGetters.TryAdd(typeof(Dictionary<string, double>), StorageCollectionDelegates.GetDictStringDoubleItem);
            _dictAddMethods.TryAdd(typeof(Dictionary<string, double>), StorageCollectionDelegates.AddToDictStringDouble);
            count += 3;

            // Dictionary<string, bool>
            _dictKeyGetters.TryAdd(typeof(Dictionary<string, bool>), StorageCollectionDelegates.GetDictStringBoolKeys);
            _dictItemGetters.TryAdd(typeof(Dictionary<string, bool>), StorageCollectionDelegates.GetDictStringBoolItem);
            _dictAddMethods.TryAdd(typeof(Dictionary<string, bool>), StorageCollectionDelegates.AddToDictStringBool);
            count += 3;

            // Dictionary<string, long>
            _dictKeyGetters.TryAdd(typeof(Dictionary<string, long>), StorageCollectionDelegates.GetDictStringLongKeys);
            _dictItemGetters.TryAdd(typeof(Dictionary<string, long>), StorageCollectionDelegates.GetDictStringLongItem);
            _dictAddMethods.TryAdd(typeof(Dictionary<string, long>), StorageCollectionDelegates.AddToDictStringLong);
            count += 3;

            // Dictionary<string, Vector2>
            _dictKeyGetters.TryAdd(typeof(Dictionary<string, Vector2>), StorageCollectionDelegates.GetDictStringVector2Keys);
            _dictItemGetters.TryAdd(typeof(Dictionary<string, Vector2>), StorageCollectionDelegates.GetDictStringVector2Item);
            _dictAddMethods.TryAdd(typeof(Dictionary<string, Vector2>), StorageCollectionDelegates.AddToDictStringVector2);
            count += 3;

            // Dictionary<string, Vector4>
            _dictKeyGetters.TryAdd(typeof(Dictionary<string, Vector4>), StorageCollectionDelegates.GetDictStringVector4Keys);
            _dictItemGetters.TryAdd(typeof(Dictionary<string, Vector4>), StorageCollectionDelegates.GetDictStringVector4Item);
            _dictAddMethods.TryAdd(typeof(Dictionary<string, Vector4>), StorageCollectionDelegates.AddToDictStringVector4);
            count += 3;

            // Dictionary<string, Quaternion>
            _dictKeyGetters.TryAdd(typeof(Dictionary<string, Quaternion>), StorageCollectionDelegates.GetDictStringQuaternionKeys);
            _dictItemGetters.TryAdd(typeof(Dictionary<string, Quaternion>), StorageCollectionDelegates.GetDictStringQuaternionItem);
            _dictAddMethods.TryAdd(typeof(Dictionary<string, Quaternion>), StorageCollectionDelegates.AddToDictStringQuaternion);
            count += 3;

            // Dictionary<string, Color32>
            _dictKeyGetters.TryAdd(typeof(Dictionary<string, Color32>), StorageCollectionDelegates.GetDictStringColor32Keys);
            _dictItemGetters.TryAdd(typeof(Dictionary<string, Color32>), StorageCollectionDelegates.GetDictStringColor32Item);
            _dictAddMethods.TryAdd(typeof(Dictionary<string, Color32>), StorageCollectionDelegates.AddToDictStringColor32);
            count += 3;

            // Dictionary<int, float>
            _dictKeyGetters.TryAdd(typeof(Dictionary<int, float>), StorageCollectionDelegates.GetDictIntFloatKeys);
            _dictItemGetters.TryAdd(typeof(Dictionary<int, float>), StorageCollectionDelegates.GetDictIntFloatItem);
            _dictAddMethods.TryAdd(typeof(Dictionary<int, float>), StorageCollectionDelegates.AddToDictIntFloat);
            count += 3;

            // Dictionary<int, double>
            _dictKeyGetters.TryAdd(typeof(Dictionary<int, double>), StorageCollectionDelegates.GetDictIntDoubleKeys);
            _dictItemGetters.TryAdd(typeof(Dictionary<int, double>), StorageCollectionDelegates.GetDictIntDoubleItem);
            _dictAddMethods.TryAdd(typeof(Dictionary<int, double>), StorageCollectionDelegates.AddToDictIntDouble);
            count += 3;

            // Dictionary<int, bool>
            _dictKeyGetters.TryAdd(typeof(Dictionary<int, bool>), StorageCollectionDelegates.GetDictIntBoolKeys);
            _dictItemGetters.TryAdd(typeof(Dictionary<int, bool>), StorageCollectionDelegates.GetDictIntBoolItem);
            _dictAddMethods.TryAdd(typeof(Dictionary<int, bool>), StorageCollectionDelegates.AddToDictIntBool);
            count += 3;

            // Dictionary<int, Vector2>
            _dictKeyGetters.TryAdd(typeof(Dictionary<int, Vector2>), StorageCollectionDelegates.GetDictIntVector2Keys);
            _dictItemGetters.TryAdd(typeof(Dictionary<int, Vector2>), StorageCollectionDelegates.GetDictIntVector2Item);
            _dictAddMethods.TryAdd(typeof(Dictionary<int, Vector2>), StorageCollectionDelegates.AddToDictIntVector2);
            count += 3;

            // Dictionary<int, Vector3>
            _dictKeyGetters.TryAdd(typeof(Dictionary<int, Vector3>), StorageCollectionDelegates.GetDictIntVector3Keys);
            _dictItemGetters.TryAdd(typeof(Dictionary<int, Vector3>), StorageCollectionDelegates.GetDictIntVector3Item);
            _dictAddMethods.TryAdd(typeof(Dictionary<int, Vector3>), StorageCollectionDelegates.AddToDictIntVector3);
            count += 3;

            return count;
        }

        /// <summary>
        /// 静态构造函数，初始化所有类型定义
        /// </summary>
        static StorageTypeInitializer()
        {
            // 定义所有支持的类型
            _supportedTypes = new Type[]
            {
                // 基础类型
                typeof(int), typeof(float), typeof(double), typeof(bool), typeof(string),
                typeof(long), typeof(char), typeof(byte), typeof(sbyte), typeof(short),
                typeof(ushort), typeof(uint), typeof(ulong), typeof(decimal),
                typeof(DateTime), typeof(Guid),

                // Unity基础类型
                typeof(Vector2), typeof(Vector3), typeof(Vector4), typeof(Quaternion),
                typeof(Color), typeof(Color32), typeof(Rect), typeof(Bounds),
                typeof(Matrix4x4), typeof(Vector2Int), typeof(Vector3Int),
                typeof(RectInt), typeof(BoundsInt), typeof(LayerMask),

                // 数组类型
                typeof(int[]), typeof(float[]), typeof(double[]), typeof(bool[]), typeof(string[]),
                typeof(long[]), typeof(char[]), typeof(byte[]), typeof(sbyte[]), typeof(short[]),
                typeof(ushort[]), typeof(uint[]), typeof(ulong[]), typeof(decimal[]),
                typeof(Vector2[]), typeof(Vector3[]), typeof(Vector4[]), typeof(Quaternion[]),
                typeof(Color[]), typeof(Color32[]),

                // List 集合类型
                typeof(List<int>), typeof(List<float>), typeof(List<double>), typeof(List<bool>), typeof(List<string>),
                typeof(List<long>), typeof(List<char>), typeof(List<byte>), typeof(List<sbyte>), typeof(List<short>),
                typeof(List<ushort>), typeof(List<uint>), typeof(List<ulong>), typeof(List<decimal>),
                typeof(List<Vector2>), typeof(List<Vector3>), typeof(List<Vector4>), typeof(List<Quaternion>),
                typeof(List<Color>), typeof(List<Color32>),

                // Dictionary 集合类型 (常用的键类型)
                typeof(Dictionary<string, int>), typeof(Dictionary<string, float>), typeof(Dictionary<string, double>),
                typeof(Dictionary<string, bool>), typeof(Dictionary<string, string>), typeof(Dictionary<string, long>),
                typeof(Dictionary<string, Vector2>), typeof(Dictionary<string, Vector3>), typeof(Dictionary<string, Vector4>),
                typeof(Dictionary<string, Quaternion>), typeof(Dictionary<string, Color>), typeof(Dictionary<string, Color32>),

                typeof(Dictionary<int, int>), typeof(Dictionary<int, float>), typeof(Dictionary<int, double>),
                typeof(Dictionary<int, bool>), typeof(Dictionary<int, string>), typeof(Dictionary<int, Vector2>),
                typeof(Dictionary<int, Vector3>), typeof(Dictionary<int, Color>)
            };

            // 初始化类型标识符映射
            _typeToIdentifier = CreateTypeIdentifierMapping();
            _identifierToType = CreateIdentifierTypeMapping(_typeToIdentifier);

            // 初始化类型工厂映射
            _typeFactories = CreateTypeFactories();

            // 初始化转换委托缓存
            _converReflectionDelegates = new ConcurrentDictionary<StorageTypeConverter.DeserializationDelagateKey, StorageTypeConverter.DeserializationTypeConvertDelegate>();

            // 初始化类型反射工厂映射
            _typeReflectionFactories = new ConcurrentDictionary<Type, Func<StorageTypeWrapper>>();

            // 初始化表达式树缓存
            _listCountGetters = new ConcurrentDictionary<Type, Func<object, int>>();
            _listItemGetters = new ConcurrentDictionary<Type, Func<object, int, object>>();
            _listAddMethods = new ConcurrentDictionary<Type, Action<object, object>>();
            _dictKeyGetters = new ConcurrentDictionary<Type, Func<object, System.Collections.IEnumerable>>();
            _dictItemGetters = new ConcurrentDictionary<Type, Func<object, object, object>>();
            _dictAddMethods = new ConcurrentDictionary<Type, Action<object, object, object>>();

            // 预缓存常见集合类型的表达式树委托
            PreCacheCommonCollectionTypes();

            NLogger.Log("StorageTypeInitializer static initialization completed with {0} supported types", arg0: _supportedTypes.Length);
        }

        #endregion

        #region 公共属性

        /// <summary>
        /// 类型到标识符的映射
        /// </summary>
        public static IReadOnlyDictionary<Type, string> TypeToIdentifier => _typeToIdentifier;

        /// <summary>
        /// 标识符到类型的映射
        /// </summary>
        public static IReadOnlyDictionary<string, Type> IdentifierToType => _identifierToType;

        /// <summary>
        /// 类型工厂映射
        /// </summary>
        public static Dictionary<Type, Func<StorageTypeWrapper>> TypeFactories => _typeFactories;

        /// <summary>
        /// 转换委托缓存
        /// </summary>
        public static ConcurrentDictionary<StorageTypeConverter.DeserializationDelagateKey, StorageTypeConverter.DeserializationTypeConvertDelegate> ConverReflectionDelegates => _converReflectionDelegates;

        /// <summary>
        /// 类型反射工厂映射
        /// </summary>
        public static ConcurrentDictionary<Type, Func<StorageTypeWrapper>> TypeReflectionFactories => _typeReflectionFactories;

        /// <summary>
        /// 所有支持的类型
        /// </summary>
        public static IReadOnlyList<Type> SupportedTypes => _supportedTypes;

        /// <summary>
        /// List Count 属性获取器缓存
        /// </summary>
        public static ConcurrentDictionary<Type, Func<object, int>> ListCountGetters => _listCountGetters;

        /// <summary>
        /// List Item 索引器获取器缓存
        /// </summary>
        public static ConcurrentDictionary<Type, Func<object, int, object>> ListItemGetters => _listItemGetters;

        /// <summary>
        /// List Add 方法缓存
        /// </summary>
        public static ConcurrentDictionary<Type, Action<object, object>> ListAddMethods => _listAddMethods;

        /// <summary>
        /// Dictionary Keys 属性获取器缓存
        /// </summary>
        public static ConcurrentDictionary<Type, Func<object, System.Collections.IEnumerable>> DictKeyGetters => _dictKeyGetters;

        /// <summary>
        /// Dictionary Item 索引器获取器缓存
        /// </summary>
        public static ConcurrentDictionary<Type, Func<object, object, object>> DictItemGetters => _dictItemGetters;

        /// <summary>
        /// Dictionary Add 方法缓存
        /// </summary>
        public static ConcurrentDictionary<Type, Action<object, object, object>> DictAddMethods => _dictAddMethods;

        #endregion

        #region 公共方法

        /// <summary>
        /// 检查类型是否被支持
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <returns>是否被支持</returns>
        public static bool IsTypeSupported(Type type)
        {
            return _typeToIdentifier.ContainsKey(type);
        }

        /// <summary>
        /// 获取类型标识符
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>类型标识符，如果不支持则返回null</returns>
        public static string GetTypeIdentifier(Type type)
        {
            return _typeToIdentifier.TryGetValue(type, out string identifier) ? identifier : null;
        }

        /// <summary>
        /// 获取标识符对应的类型
        /// </summary>
        /// <param name="identifier">类型标识符</param>
        /// <returns>类型，如果不存在则返回null</returns>
        public static Type GetTypeFromIdentifier(string identifier)
        {
            return _identifierToType.TryGetValue(identifier, out Type type) ? type : null;
        }

        /// <summary>
        /// 获取或创建 List Count 属性获取器
        /// </summary>
        /// <param name="listType">List 类型</param>
        /// <returns>Count 属性获取器委托</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Func<object, int> GetOrCreateListCountGetter(Type listType)
        {
            return _listCountGetters.GetOrAdd(listType, CreateListCountGetter);
        }

        /// <summary>
        /// 创建 List Count 属性获取器委托
        /// </summary>
        /// <param name="listType">List 类型</param>
        /// <returns>Count 属性获取器委托</returns>
        private static Func<object, int> CreateListCountGetter(Type listType)
        {
#if !UNITY_IOS
            try
            {
                var countProperty = listType.GetProperty("Count");
                if (countProperty == null)
                {
                    return null;
                }

                var objParam = Expression.Parameter(typeof(object), "obj");
                var castObj = Expression.Convert(objParam, listType);
                var getCount = Expression.Property(castObj, countProperty);
                var lambda = Expression.Lambda<Func<object, int>>(getCount, objParam);
                return lambda.Compile();
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Failed to create expression delegate for List Count getter on type '{0}', falling back to reflection: {1}",
                    arg0: listType.Name, arg1: ex.Message);
#endif
                // 回退到反射方式
                var countProperty = listType.GetProperty("Count");
                return countProperty != null ? (obj) => (int)countProperty.GetValue(obj) : null;
#if !UNITY_IOS
            }
#endif
        }

        /// <summary>
        /// 获取或创建 List Item 索引器获取器
        /// </summary>
        /// <param name="listType">List 类型</param>
        /// <returns>Item 索引器获取器委托</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Func<object, int, object> GetOrCreateListItemGetter(Type listType)
        {
            return _listItemGetters.GetOrAdd(listType, CreateListItemGetter);
        }

        /// <summary>
        /// 创建 List Item 索引器获取器委托
        /// </summary>
        /// <param name="listType">List 类型</param>
        /// <returns>Item 索引器获取器委托</returns>
        private static Func<object, int, object> CreateListItemGetter(Type listType)
        {
#if !UNITY_IOS
            try
            {
                var indexer = listType.GetProperty("Item");
                if (indexer == null)
                {
                    return null;
                }

                var objParam = Expression.Parameter(typeof(object), "obj");
                var indexParam = Expression.Parameter(typeof(int), "index");
                var castObj = Expression.Convert(objParam, listType);
                var getItem = Expression.Property(castObj, indexer, indexParam);
                var castResult = Expression.Convert(getItem, typeof(object));
                var lambda = Expression.Lambda<Func<object, int, object>>(castResult, objParam, indexParam);
                return lambda.Compile();
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Failed to create expression delegate for List Item getter on type '{0}', falling back to reflection: {1}",
                    arg0: listType.Name, arg1: ex.Message);
#endif
                // 回退到反射方式
                var indexer = listType.GetProperty("Item");
                return indexer != null ? (obj, index) => indexer.GetValue(obj, new object[] { index }) : null;
#if !UNITY_IOS
            }
#endif
        }

        /// <summary>
        /// 获取或创建 List Add 方法
        /// </summary>
        /// <param name="listType">List 类型</param>
        /// <returns>Add 方法委托</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Action<object, object> GetOrCreateListAddMethod(Type listType)
        {
            return _listAddMethods.GetOrAdd(listType, CreateListAddMethod);
        }

        /// <summary>
        /// 创建 List Add 方法委托
        /// </summary>
        /// <param name="listType">List 类型</param>
        /// <returns>Add 方法委托</returns>
        private static Action<object, object> CreateListAddMethod(Type listType)
        {
#if !UNITY_IOS
            try
            {
                var addMethod = listType.GetMethod("Add");
                if (addMethod == null)
                {
                    return null;
                }

                var elementType = listType.GetGenericArguments()[0];
                var objParam = Expression.Parameter(typeof(object), "obj");
                var itemParam = Expression.Parameter(typeof(object), "item");
                var castObj = Expression.Convert(objParam, listType);
                var castItem = Expression.Convert(itemParam, elementType);
                var callAdd = Expression.Call(castObj, addMethod, castItem);
                var lambda = Expression.Lambda<Action<object, object>>(callAdd, objParam, itemParam);
                return lambda.Compile();
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Failed to create expression delegate for List Add method on type '{0}', falling back to reflection: {1}",
                    arg0: listType.Name, arg1: ex.Message);
#endif
                // 回退到反射方式
                var addMethod = listType.GetMethod("Add");
                return addMethod != null ? (obj, item) => addMethod.Invoke(obj, new object[] { item }) : null;
#if !UNITY_IOS
            }
#endif
        }



        /// <summary>
        /// 获取或创建 Dictionary Keys 属性获取器
        /// </summary>
        /// <param name="dictType">Dictionary 类型</param>
        /// <returns>Keys 属性获取器委托</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Func<object, System.Collections.IEnumerable> GetOrCreateDictKeyGetter(Type dictType)
        {
            return _dictKeyGetters.GetOrAdd(dictType, CreateDictKeyGetter);
        }

        /// <summary>
        /// 创建 Dictionary Keys 属性获取器委托
        /// </summary>
        /// <param name="dictType">Dictionary 类型</param>
        /// <returns>Keys 属性获取器委托</returns>
        private static Func<object, System.Collections.IEnumerable> CreateDictKeyGetter(Type dictType)
        {
#if !UNITY_IOS
            try
            {
                var keysProperty = dictType.GetProperty("Keys");
                if (keysProperty == null)
                {
                    return null;
                }

                var objParam = Expression.Parameter(typeof(object), "obj");
                var castObj = Expression.Convert(objParam, dictType);
                var getKeys = Expression.Property(castObj, keysProperty);
                var castResult = Expression.Convert(getKeys, typeof(System.Collections.IEnumerable));
                var lambda = Expression.Lambda<Func<object, System.Collections.IEnumerable>>(castResult, objParam);
                return lambda.Compile();
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Failed to create expression delegate for Dictionary Keys getter on type '{0}', falling back to reflection: {1}",
                    arg0: dictType.Name, arg1: ex.Message);
#endif
                // 回退到反射方式
                var keysProperty = dictType.GetProperty("Keys");
                return keysProperty != null ? (obj) => keysProperty.GetValue(obj) as System.Collections.IEnumerable : null;
#if !UNITY_IOS
            }
#endif
        }

        /// <summary>
        /// 获取或创建 Dictionary Item 索引器获取器
        /// </summary>
        /// <param name="dictType">Dictionary 类型</param>
        /// <returns>Item 索引器获取器委托</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Func<object, object, object> GetOrCreateDictItemGetter(Type dictType)
        {
            return _dictItemGetters.GetOrAdd(dictType, CreateDictItemGetter);
        }

        /// <summary>
        /// 创建 Dictionary Item 索引器获取器委托
        /// </summary>
        /// <param name="dictType">Dictionary 类型</param>
        /// <returns>Item 索引器获取器委托</returns>
        private static Func<object, object, object> CreateDictItemGetter(Type dictType)
        {
#if !UNITY_IOS
            try
            {
                var indexer = dictType.GetProperty("Item");
                if (indexer == null)
                {
                    return null;
                }

                var keyType = dictType.GetGenericArguments()[0];
                var objParam = Expression.Parameter(typeof(object), "obj");
                var keyParam = Expression.Parameter(typeof(object), "key");
                var castObj = Expression.Convert(objParam, dictType);
                var castKey = Expression.Convert(keyParam, keyType);
                var getItem = Expression.Property(castObj, indexer, castKey);
                var castResult = Expression.Convert(getItem, typeof(object));
                var lambda = Expression.Lambda<Func<object, object, object>>(castResult, objParam, keyParam);
                return lambda.Compile();
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Failed to create expression delegate for Dictionary Item getter on type '{0}', falling back to reflection: {1}",
                    arg0: dictType.Name, arg1: ex.Message);
#endif
                // 回退到反射方式
                var indexer = dictType.GetProperty("Item");
                return indexer != null ? (obj, key) => indexer.GetValue(obj, new object[] { key }) : null;
#if !UNITY_IOS
            }
#endif
        }

        /// <summary>
        /// 获取或创建 Dictionary Add 方法
        /// </summary>
        /// <param name="dictType">Dictionary 类型</param>
        /// <returns>Add 方法委托</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Action<object, object, object> GetOrCreateDictAddMethod(Type dictType)
        {
            return _dictAddMethods.GetOrAdd(dictType, CreateDictAddMethod);
        }

        /// <summary>
        /// 创建 Dictionary Add 方法委托
        /// </summary>
        /// <param name="dictType">Dictionary 类型</param>
        /// <returns>Add 方法委托</returns>
        private static Action<object, object, object> CreateDictAddMethod(Type dictType)
        {
#if !UNITY_IOS
            try
            {
                var genericArgs = dictType.GetGenericArguments();
                var addMethod = dictType.GetMethod("Add", new Type[] { genericArgs[0], genericArgs[1] });
                if (addMethod == null)
                {
                    return null;
                }

                var keyType = genericArgs[0];
                var valueType = genericArgs[1];
                var objParam = Expression.Parameter(typeof(object), "obj");
                var keyParam = Expression.Parameter(typeof(object), "key");
                var valueParam = Expression.Parameter(typeof(object), "value");
                var castObj = Expression.Convert(objParam, dictType);
                var castKey = Expression.Convert(keyParam, keyType);
                var castValue = Expression.Convert(valueParam, valueType);
                var callAdd = Expression.Call(castObj, addMethod, castKey, castValue);
                var lambda = Expression.Lambda<Action<object, object, object>>(callAdd, objParam, keyParam, valueParam);
                return lambda.Compile();
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Failed to create expression delegate for Dictionary Add method on type '{0}', falling back to reflection: {1}",
                    arg0: dictType.Name, arg1: ex.Message);
#endif
                // 回退到反射方式
                var genericArgs = dictType.GetGenericArguments();
                var addMethod = dictType.GetMethod("Add", new Type[] { genericArgs[0], genericArgs[1] });
                return addMethod != null ? (obj, key, value) => addMethod.Invoke(obj, new object[] { key, value }) : null;
#if !UNITY_IOS
            }
#endif
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 创建标识符到类型的映射
        /// </summary>
        /// <param name="typeToIdentifier">类型到标识符的映射</param>
        /// <returns>标识符到类型的映射字典</returns>
        private static Dictionary<string, Type> CreateIdentifierTypeMapping(Dictionary<Type, string> typeToIdentifier)
        {
            var mapping = new Dictionary<string, Type>();
            foreach (var kvp in typeToIdentifier)
            {
                mapping[kvp.Value] = kvp.Key;
            }
            return mapping;
        }

        #endregion
    }
}