using System;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Runtime.CompilerServices;
using DGame.Framework;
using Storage.Serialization;

namespace Storage
{
    /// <summary>
    /// 存储系统的内存缓存管理器，负责管理内存中的数据
    /// 使用类型包装器系统避免值类型的装箱拆箱操作，提高性能并减少GC压力
    /// 性能优化：添加类型缓存和包装器复用机制
    /// </summary>
    public class StorageCache
    {
        #region 私有字段

        // 使用StorageTypeWrapper避免装箱拆箱操作
        private readonly Dictionary<string, StorageTypeWrapper> _cache;
        private bool _isDirty = false;

        // 性能优化：类型缓存，避免重复的Type.GetType()调用和类型检查
        private readonly ConcurrentDictionary<string, Type> _keyTypeCache = new ConcurrentDictionary<string, Type>();

        // 性能优化：包装器类型缓存，避免重复的GetValueType()调用
        private readonly ConcurrentDictionary<string, Type> _wrapperTypeCache = new ConcurrentDictionary<string, Type>();

        #endregion

        #region 属性
        /// <summary>
        /// 缓存是否已被修改
        /// </summary>
        public bool IsDirty
        {
            get { return _isDirty; }
        }

        /// <summary>
        /// 缓存中的数据数量
        /// </summary>
        public int Count
        {
            get { return _cache.Count; }
        }

        /// <summary>
        /// 获取所有的键
        /// </summary>
        public ICollection<string> Keys
        {
            get { return _cache.Keys; }
        }

        #endregion

        #region 构造函数
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public StorageCache()
        {
            _cache = new Dictionary<string, StorageTypeWrapper>();
        }

        /// <summary>
        /// 带初始容量的构造函数
        /// </summary>
        /// <param name="initialCapacity">初始容量</param>
        public StorageCache(int initialCapacity)
        {
            _cache = new Dictionary<string, StorageTypeWrapper>(initialCapacity);
        }

        #endregion

        // 注意：类型转换逻辑已移至StorageTypeConverter类中

        #region 数据操作方法

        /// <summary>
        /// 设置数据（使用类型包装器避免装箱）
        /// 性能优化：使用类型缓存减少重复的类型检查和包装器创建
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Set<T>(string key, T value)
        {
            Type type = value == null ? typeof(T) : value.GetType();

            if (_cache.TryGetValue(key, out var existingWrapper))
            {
                // 性能优化：使用缓存的包装器类型，避免重复调用GetValueType()
                Type cachedWrapperType = _wrapperTypeCache.GetOrAdd(key, _ => existingWrapper.GetValueType());

                // 如果类型匹配，直接设置值（避免重新创建包装器）
                if (cachedWrapperType == type)
                {
                    existingWrapper.SetValue(value);
                }
                else
                {
                    // 类型不匹配，创建新的包装器并更新缓存
                    var newWrapper = StorageTypeMgr.CreateWrapperForType(type, value);
                    _cache[key] = newWrapper;
                    _wrapperTypeCache[key] = type; // 更新类型缓存
                }
            }
            else
            {
                // 新键，创建包装器并缓存类型
                var newWrapper = StorageTypeMgr.CreateWrapperForType(type, value);
                _cache[key] = newWrapper;
                _wrapperTypeCache[key] = type; // 缓存类型信息
            }
            _isDirty = true;
        }

        /// <summary>
        /// 尝试获取数据（使用类型包装器避免拆箱）
        /// 性能优化：减少日志开销，使用条件编译优化Release模式
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">输出的数据值</param>
        /// <returns>是否成功获取到值</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public bool TryGet<T>(string key, out T value)
        {
            if (_cache.TryGetValue(key, out var wrapper))
            {
                try
                {
                    // 使用包装器的类型安全方法获取值
                    if (wrapper.TryGetValue(out value))
                    {
                        return true;
                    }

#if UNITY_EDITOR || DEBUG
                    // 性能优化：仅在Debug模式下记录详细日志，避免Release模式的字符串分配
                    NLogger.LogWarning("Cannot convert value for key '{0}' from type {1} to type {2}. Cached value: {3}",
                        arg0: key, arg1: wrapper.GetValueType().Name, arg2: typeof(T).Name, arg3: wrapper.GetValueString());
#endif
                    return false;
                }
                catch (Exception ex)
                {
                    value = default;
#if UNITY_EDITOR || DEBUG
                    // 性能优化：仅在Debug模式下记录异常日志
                    NLogger.LogError("Error getting value for key '{0}': {1}", arg0: key, arg1: ex.Message);
#endif
                    return false;
                }
            }

            value = default;
#if UNITY_EDITOR || DEBUG
            // 性能优化：仅在Debug模式下记录键未找到的警告
            NLogger.LogWarning("Key '{0}' not found", arg0: key);
#endif
            return false;
        }

        /// <summary>
        /// 检查键是否存在
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>是否存在</returns>
        public bool ContainsKey(string key)
        {
            return _cache.ContainsKey(key);
        }

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>是否删除成功</returns>
        public bool Remove(string key)
        {
            bool removed = _cache.Remove(key);
            if (removed)
            {
                _isDirty = true;
                NLogger.Log("Key '{0}' removed successfully", arg0: key);
            }
            else
            {
                NLogger.LogWarning("Key '{0}' not found", arg0: key);
            }
            return removed;
        }

        /// <summary>
        /// 清空所有数据
        /// </summary>
        public void Clear()
        {
            int count = _cache.Count;
            _cache.Clear();
            _isDirty = true;
            NLogger.Log("Cleared {0} items from cache", arg0: count);
        }

        /// <summary>
        /// 获取所有数据的副本（用于序列化，使用新的内嵌类型格式）
        /// </summary>
        /// <returns>内嵌类型格式的数据字典</returns>
        public Dictionary<string, object> GetSerializeData()
        {
            return StorageTypeMetadata.SaveWithInlineMetadata(_cache);
        }

        /// <summary>
        /// 直接从StorageTypeWrapper字典加载数据
        /// 用于优化异步加载性能，避免重复的类型转换
        /// </summary>
        /// <param name="wrapperData">StorageTypeWrapper字典</param>
        public void LoadFromDictionary(Dictionary<string, StorageTypeWrapper> wrapperData)
        {
            if (wrapperData == null)
            {
                NLogger.LogError("Wrapper data dictionary cannot be null");
                return;
            }

            _cache.Clear();

            // 直接复制包装器数据，无需类型转换
            foreach (var kvp in wrapperData)
            {
                _cache[kvp.Key] = kvp.Value;
            }

            _isDirty = false;
            NLogger.Log("Loaded {0} items into cache from wrapper dictionary", arg0: _cache.Count);
        }

        /// <summary>
        /// 标记缓存为已保存状态
        /// </summary>
        public void MarkAsSaved()
        {
            _isDirty = false;
        }

        /// <summary>
        /// 强制标记缓存为脏状态
        /// </summary>
        public void MarkAsDirty()
        {
            _isDirty = true;
        }

        #endregion

        #region 调试方法

        /// <summary>
        /// 获取缓存状态信息
        /// </summary>
        /// <returns>状态信息字符串</returns>
        public string GetStatusInfo()
        {
            return $"StorageCache Status - Count: {Count}, IsDirty: {IsDirty}";
        }

        /// <summary>
        /// 打印所有缓存的键
        /// </summary>
        public void LogAllKeys()
        {
            var keys = string.Join(", ", Keys);
            NLogger.Log("All Keys: [{0}]", arg0: keys);
        }

        #endregion
    }
}
