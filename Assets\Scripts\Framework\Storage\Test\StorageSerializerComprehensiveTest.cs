using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Concurrent;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using UnityEngine;
using Storage;
using DGame.Framework;

namespace Storage.Test
{
    /// <summary>
    /// Storage序列化器全面测试脚本
    /// 测试所有支持的数据类型、往返序列化、边界条件和错误处理
    ///
    /// 新增功能：
    /// - 持久化功能测试：测试数据保存到文件后的完整读取恢复
    /// - 复杂类型测试：包括继承、泛型、深度嵌套、多维数组等高级数据结构
    /// - 加密功能测试：测试AES和XOR加密的数据保存和读取
    /// - 高级数据比较：支持复杂集合类型的精确比较验证
    /// </summary>
    public class StorageSerializerComprehensiveTest : MonoBehaviour
    {
        #region 测试配置

        [Header("测试配置")]
        [SerializeField] private bool _runTestsOnStart = true;
        [SerializeField] private bool _enableDetailedLogging = true;
        [SerializeField] private bool _runPerformanceTests = true;
        [SerializeField] private int _performanceIterations = 1000;

        [Header("测试控制")]
        [SerializeField] private bool _testBasicTypes = true;
        [SerializeField] private bool _testUnityTypes = true;
        [SerializeField] private bool _testCollectionTypes = true;
        [SerializeField] private bool _testCustomTypes = true;
        [SerializeField] private bool _testNestedTypes = true;
        [SerializeField] private bool _testNullValues = true;
        [SerializeField] private bool _testBoundaryConditions = true;
        [SerializeField] private bool _testErrorHandling = true;
        [SerializeField] private bool _testPersistence = true;
        [SerializeField] private bool _testAdvancedTypes = true;
        [SerializeField] private bool _testEncryption = true;
        [SerializeField] private bool _testConcurrency = true;

        [Header("并发测试配置")]
        [SerializeField] private int _concurrentThreadCount = 10;
        [SerializeField] private int _concurrentOperationsPerThread = 100;
        [SerializeField] private float _concurrentTestTimeout = 30f;
        [SerializeField] private bool _enableConcurrentDetailedLogging = false;
        [SerializeField] private bool _testSameInstanceConcurrency = true;
        [SerializeField] private bool _testDifferentInstancesConcurrency = true;
        [SerializeField] private bool _testReadWriteConcurrency = true;
        [SerializeField] private bool _testCacheConcurrency = true;
        [SerializeField] private bool _testFileOperationConcurrency = true;
        [SerializeField] private bool _testTypeWrapperPerformance = true;

        #endregion

        #region 测试数据类型定义

        /// <summary>
        /// 简单测试类
        /// </summary>
        [Serializable]
        public class SimpleTestClass
        {
            public int intValue;
            public float floatValue;
            public string stringValue;
            public bool boolValue;

            public SimpleTestClass() { }

            public SimpleTestClass(int i, float f, string s, bool b)
            {
                intValue = i;
                floatValue = f;
                stringValue = s;
                boolValue = b;
            }

            public override bool Equals(object obj)
            {
                if (obj is SimpleTestClass other)
                {
                    return intValue == other.intValue &&
                           Mathf.Approximately(floatValue, other.floatValue) &&
                           stringValue == other.stringValue &&
                           boolValue == other.boolValue;
                }
                return false;
            }

            public override int GetHashCode()
            {
                return HashCode.Combine(intValue, floatValue, stringValue, boolValue);
            }
        }

        /// <summary>
        /// 复杂嵌套测试类
        /// </summary>
        [Serializable]
        public class ComplexTestClass
        {
            public SimpleTestClass simple;
            public List<int> numbers;
            public Dictionary<string, float> floatDict;
            public Vector3[] positions;
            public Color[] colors;

            public ComplexTestClass()
            {
                numbers = new List<int>();
                floatDict = new Dictionary<string, float>();
            }

            public override bool Equals(object obj)
            {
                if (obj is ComplexTestClass other)
                {
                    // 比较simple对象
                    if (!Equals(simple, other.simple)) return false;

                    // 比较numbers列表
                    if (numbers?.Count != other.numbers?.Count) return false;
                    if (numbers != null)
                    {
                        for (int i = 0; i < numbers.Count; ++i)
                        {
                            if (numbers[i] != other.numbers[i]) return false;
                        }
                    }

                    // 比较floatDict字典
                    if (floatDict?.Count != other.floatDict?.Count) return false;
                    if (floatDict != null)
                    {
                        foreach (var kvp in floatDict)
                        {
                            if (!other.floatDict.ContainsKey(kvp.Key) ||
                                !Mathf.Approximately(kvp.Value, other.floatDict[kvp.Key]))
                                return false;
                        }
                    }

                    // 比较positions数组
                    if (positions?.Length != other.positions?.Length) return false;
                    if (positions != null)
                    {
                        for (int i = 0; i < positions.Length; ++i)
                        {
                            if (Vector3.Distance(positions[i], other.positions[i]) > 0.001f) return false;
                        }
                    }

                    // 比较colors数组
                    if (colors?.Length != other.colors?.Length) return false;
                    if (colors != null)
                    {
                        for (int i = 0; i < colors.Length; ++i)
                        {
                            if (!ColorsEqual(colors[i], other.colors[i])) return false;
                        }
                    }

                    return true;
                }
                return false;
            }

            private static bool ColorsEqual(Color a, Color b)
            {
                return Mathf.Approximately(a.r, b.r) &&
                       Mathf.Approximately(a.g, b.g) &&
                       Mathf.Approximately(a.b, b.b) &&
                       Mathf.Approximately(a.a, b.a);
            }

            public override int GetHashCode()
            {
                return HashCode.Combine(simple, numbers, floatDict, positions, colors);
            }
        }

        /// <summary>
        /// 测试结构体
        /// </summary>
        [Serializable]
        public struct TestStruct
        {
            public int id;
            public Vector2 position;
            public Color color;

            public TestStruct(int i, Vector2 pos, Color col)
            {
                id = i;
                position = pos;
                color = col;
            }

            public override bool Equals(object obj)
            {
                if (obj is TestStruct other)
                {
                    return id == other.id &&
                           Vector2.Distance(position, other.position) < 0.001f &&
                           ColorsEqual(color, other.color);
                }
                return false;
            }

            private static bool ColorsEqual(Color a, Color b)
            {
                return Mathf.Approximately(a.r, b.r) &&
                       Mathf.Approximately(a.g, b.g) &&
                       Mathf.Approximately(a.b, b.b) &&
                       Mathf.Approximately(a.a, b.a);
            }

            public override int GetHashCode()
            {
                return HashCode.Combine(id, position, color);
            }
        }

        /// <summary>
        /// 高级测试基类
        /// </summary>
        [Serializable]
        public abstract class AdvancedTestBase
        {
            public string baseId;
            public DateTime timestamp;

            protected AdvancedTestBase()
            {
                timestamp = DateTime.Now;
            }

            protected AdvancedTestBase(string id)
            {
                baseId = id;
                timestamp = DateTime.Now;
            }

            public abstract string GetTypeInfo();

            public override bool Equals(object obj)
            {
                if (obj is AdvancedTestBase other)
                {
                    return baseId == other.baseId &&
                           Math.Abs((timestamp - other.timestamp).TotalMilliseconds) < 1000; // 允许1秒误差
                }
                return false;
            }

            public override int GetHashCode()
            {
                return HashCode.Combine(baseId, timestamp);
            }
        }

        /// <summary>
        /// 继承测试类A
        /// </summary>
        [Serializable]
        public class InheritanceTestA : AdvancedTestBase
        {
            public int intValue;
            public float floatValue;

            public InheritanceTestA() : base() { }

            public InheritanceTestA(string id, int i, float f) : base(id)
            {
                intValue = i;
                floatValue = f;
            }

            public override string GetTypeInfo()
            {
                return "InheritanceTestA";
            }

            public override bool Equals(object obj)
            {
                if (obj is InheritanceTestA other && base.Equals(obj))
                {
                    return intValue == other.intValue &&
                           Mathf.Approximately(floatValue, other.floatValue);
                }
                return false;
            }

            public override int GetHashCode()
            {
                return HashCode.Combine(base.GetHashCode(), intValue, floatValue);
            }
        }

        /// <summary>
        /// 继承测试类B
        /// </summary>
        [Serializable]
        public class InheritanceTestB : AdvancedTestBase
        {
            public Vector3 position;
            public List<string> tags;

            public InheritanceTestB() : base()
            {
                tags = new List<string>();
            }

            public InheritanceTestB(string id, Vector3 pos) : base(id)
            {
                position = pos;
                tags = new List<string>();
            }

            public override string GetTypeInfo()
            {
                return "InheritanceTestB";
            }

            public override bool Equals(object obj)
            {
                if (obj is InheritanceTestB other && base.Equals(obj))
                {
                    if (Vector3.Distance(position, other.position) > 0.001f) return false;

                    if (tags?.Count != other.tags?.Count) return false;
                    if (tags != null)
                    {
                        for (int i = 0; i < tags.Count; ++i)
                        {
                            if (tags[i] != other.tags[i]) return false;
                        }
                    }
                    return true;
                }
                return false;
            }

            public override int GetHashCode()
            {
                return HashCode.Combine(base.GetHashCode(), position, tags);
            }
        }

        /// <summary>
        /// 泛型测试类
        /// </summary>
        [Serializable]
        public class GenericTestClass<T> where T : IComparable<T>
        {
            public T value;
            public List<T> values;
            public Dictionary<string, T> namedValues;

            public GenericTestClass()
            {
                values = new List<T>();
                namedValues = new Dictionary<string, T>();
            }

            public GenericTestClass(T val)
            {
                value = val;
                values = new List<T>();
                namedValues = new Dictionary<string, T>();
            }

            public override bool Equals(object obj)
            {
                if (obj is GenericTestClass<T> other)
                {
                    // 比较value
                    if (!EqualityComparer<T>.Default.Equals(value, other.value)) return false;

                    // 比较values列表
                    if (values?.Count != other.values?.Count) return false;
                    if (values != null)
                    {
                        for (int i = 0; i < values.Count; ++i)
                        {
                            if (!EqualityComparer<T>.Default.Equals(values[i], other.values[i])) return false;
                        }
                    }

                    // 比较namedValues字典
                    if (namedValues?.Count != other.namedValues?.Count) return false;
                    if (namedValues != null)
                    {
                        foreach (var kvp in namedValues)
                        {
                            if (!other.namedValues.ContainsKey(kvp.Key) ||
                                !EqualityComparer<T>.Default.Equals(kvp.Value, other.namedValues[kvp.Key]))
                                return false;
                        }
                    }

                    return true;
                }
                return false;
            }

            public override int GetHashCode()
            {
                return HashCode.Combine(value, values, namedValues);
            }
        }

        /// <summary>
        /// 深度嵌套测试类
        /// </summary>
        [Serializable]
        public class DeepNestedTestClass
        {
            public int level;
            public string data;
            public DeepNestedTestClass child;
            public List<DeepNestedTestClass> children;
            public Dictionary<string, DeepNestedTestClass> namedChildren;

            public DeepNestedTestClass()
            {
                children = new List<DeepNestedTestClass>();
                namedChildren = new Dictionary<string, DeepNestedTestClass>();
            }

            public DeepNestedTestClass(int lvl, string d) : this()
            {
                level = lvl;
                data = d;
            }

            /// <summary>
            /// 创建指定深度的嵌套结构
            /// </summary>
            public static DeepNestedTestClass CreateNestedStructure(int maxDepth)
            {
                if (maxDepth <= 0) return null;

                var root = new DeepNestedTestClass(0, "root");
                var current = root;

                for (int i = 1; i < maxDepth; ++i)
                {
                    current.child = new DeepNestedTestClass(i, $"child_{i}");
                    current.children.Add(new DeepNestedTestClass(i, $"list_child_{i}_0"));
                    current.children.Add(new DeepNestedTestClass(i, $"list_child_{i}_1"));
                    current.namedChildren[$"named_child_{i}"] = new DeepNestedTestClass(i, $"named_child_{i}");
                    current = current.child;
                }

                return root;
            }

            public override bool Equals(object obj)
            {
                if (obj is DeepNestedTestClass other)
                {
                    if (level != other.level || data != other.data) return false;

                    // 比较child
                    if (!Equals(child, other.child)) return false;

                    // 比较children列表
                    if (children?.Count != other.children?.Count) return false;
                    if (children != null)
                    {
                        for (int i = 0; i < children.Count; ++i)
                        {
                            if (!Equals(children[i], other.children[i])) return false;
                        }
                    }

                    // 比较namedChildren字典
                    if (namedChildren?.Count != other.namedChildren?.Count) return false;
                    if (namedChildren != null)
                    {
                        foreach (var kvp in namedChildren)
                        {
                            if (!other.namedChildren.ContainsKey(kvp.Key) ||
                                !Equals(kvp.Value, other.namedChildren[kvp.Key]))
                                return false;
                        }
                    }

                    return true;
                }
                return false;
            }

            public override int GetHashCode()
            {
                return HashCode.Combine(level, data, child, children, namedChildren);
            }
        }

        /// <summary>
        /// 多维数组测试类
        /// </summary>
        [Serializable]
        public class MultiDimensionalTestClass
        {
            public int[,] matrix2D;
            public int[,,] matrix3D;
            public string[][] jaggedArray;
            public List<int[]> listOfArrays;
            public Dictionary<string, float[]> dictOfArrays;

            public MultiDimensionalTestClass()
            {
                listOfArrays = new List<int[]>();
                dictOfArrays = new Dictionary<string, float[]>();
            }

            public override bool Equals(object obj)
            {
                if (obj is MultiDimensionalTestClass other)
                {
                    // 比较2D矩阵
                    if (!ArraysEqual(matrix2D, other.matrix2D)) return false;

                    // 比较3D矩阵
                    if (!ArraysEqual(matrix3D, other.matrix3D)) return false;

                    // 比较锯齿数组
                    if (!JaggedArraysEqual(jaggedArray, other.jaggedArray)) return false;

                    // 比较列表数组
                    if (listOfArrays?.Count != other.listOfArrays?.Count) return false;
                    if (listOfArrays != null)
                    {
                        for (int i = 0; i < listOfArrays.Count; ++i)
                        {
                            if (!ArraysEqual(listOfArrays[i], other.listOfArrays[i])) return false;
                        }
                    }

                    // 比较字典数组
                    if (dictOfArrays?.Count != other.dictOfArrays?.Count) return false;
                    if (dictOfArrays != null)
                    {
                        foreach (var kvp in dictOfArrays)
                        {
                            if (!other.dictOfArrays.ContainsKey(kvp.Key) ||
                                !ArraysEqual(kvp.Value, other.dictOfArrays[kvp.Key]))
                                return false;
                        }
                    }

                    return true;
                }
                return false;
            }

            private static bool ArraysEqual<T>(T[,] a, T[,] b)
            {
                if (a == null && b == null) return true;
                if (a == null || b == null) return false;
                if (a.GetLength(0) != b.GetLength(0) || a.GetLength(1) != b.GetLength(1)) return false;

                for (int i = 0; i < a.GetLength(0); ++i)
                {
                    for (int j = 0; j < a.GetLength(1); ++j)
                    {
                        if (!EqualityComparer<T>.Default.Equals(a[i, j], b[i, j])) return false;
                    }
                }
                return true;
            }

            private static bool ArraysEqual<T>(T[,,] a, T[,,] b)
            {
                if (a == null && b == null) return true;
                if (a == null || b == null) return false;
                if (a.GetLength(0) != b.GetLength(0) || a.GetLength(1) != b.GetLength(1) || a.GetLength(2) != b.GetLength(2)) return false;

                for (int i = 0; i < a.GetLength(0); ++i)
                {
                    for (int j = 0; j < a.GetLength(1); ++j)
                    {
                        for (int k = 0; k < a.GetLength(2); ++k)
                        {
                            if (!EqualityComparer<T>.Default.Equals(a[i, j, k], b[i, j, k])) return false;
                        }
                    }
                }
                return true;
            }

            private static bool ArraysEqual<T>(T[] a, T[] b)
            {
                if (a == null && b == null) return true;
                if (a == null || b == null) return false;
                if (a.Length != b.Length) return false;

                for (int i = 0; i < a.Length; ++i)
                {
                    if (!EqualityComparer<T>.Default.Equals(a[i], b[i])) return false;
                }
                return true;
            }

            private static bool JaggedArraysEqual<T>(T[][] a, T[][] b)
            {
                if (a == null && b == null) return true;
                if (a == null || b == null) return false;
                if (a.Length != b.Length) return false;

                for (int i = 0; i < a.Length; ++i)
                {
                    if (!ArraysEqual(a[i], b[i])) return false;
                }
                return true;
            }

            public override int GetHashCode()
            {
                return HashCode.Combine(matrix2D, matrix3D, jaggedArray, listOfArrays, dictOfArrays);
            }
        }

        #endregion

        #region 测试结果统计

        /// <summary>
        /// 测试结果类
        /// </summary>
        [Serializable]
        public class TestResult
        {
            public string testName;
            public bool passed;
            public string errorMessage;
            public float executionTime;

            public TestResult(string name, bool success, string error = "", float time = 0f)
            {
                testName = name;
                passed = success;
                errorMessage = error;
                executionTime = time;
            }
        }

        private readonly List<TestResult> _testResults = new List<TestResult>();
        private int _totalTests = 0;
        private int _passedTests = 0;

        #endregion

        #region 并发测试辅助类和数据结构

        /// <summary>
        /// 并发测试结果类
        /// </summary>
        [Serializable]
        public class ConcurrentTestResult
        {
            public string testName;
            public bool passed;
            public string errorMessage;
            public float executionTime;
            public int threadCount;
            public int operationsPerThread;
            public int totalOperations;
            public int successfulOperations;
            public int failedOperations;
            public float operationsPerSecond;
            public long memoryUsedBytes;

            public ConcurrentTestResult(string name)
            {
                testName = name;
                passed = false;
                errorMessage = string.Empty;
                executionTime = 0f;
                threadCount = 0;
                operationsPerThread = 0;
                totalOperations = 0;
                successfulOperations = 0;
                failedOperations = 0;
                operationsPerSecond = 0f;
                memoryUsedBytes = 0;
            }
        }

        /// <summary>
        /// 线程安全的操作计数器
        /// </summary>
        public class ThreadSafeCounter
        {
            private int _successCount = 0;
            private int _failureCount = 0;
            private readonly object _lock = new object();

            public int SuccessCount
            {
                get
                {
                    lock (_lock)
                    {
                        return _successCount;
                    }
                }
            }

            public int FailureCount
            {
                get
                {
                    lock (_lock)
                    {
                        return _failureCount;
                    }
                }
            }

            public int TotalCount
            {
                get
                {
                    lock (_lock)
                    {
                        return _successCount + _failureCount;
                    }
                }
            }

            public void IncrementSuccess()
            {
                lock (_lock)
                {
                    _successCount++;
                }
            }

            public void IncrementFailure()
            {
                lock (_lock)
                {
                    _failureCount++;
                }
            }

            public void Reset()
            {
                lock (_lock)
                {
                    _successCount = 0;
                    _failureCount = 0;
                }
            }
        }

        /// <summary>
        /// 并发测试异常收集器
        /// </summary>
        public class ConcurrentExceptionCollector
        {
            private readonly ConcurrentQueue<Exception> _exceptions = new ConcurrentQueue<Exception>();

            public void AddException(Exception ex)
            {
                _exceptions.Enqueue(ex);
            }

            public bool HasExceptions
            {
                get { return !_exceptions.IsEmpty; }
            }

            public int ExceptionCount
            {
                get { return _exceptions.Count; }
            }

            public List<Exception> GetAllExceptions()
            {
                var exceptions = new List<Exception>();
                while (_exceptions.TryDequeue(out Exception ex))
                {
                    exceptions.Add(ex);
                }
                return exceptions;
            }
        }

        /// <summary>
        /// 并发测试数据验证器
        /// </summary>
        public class ConcurrentDataValidator
        {
            private readonly Dictionary<string, object> _expectedData = new Dictionary<string, object>();
            private readonly object _lock = new object();

            public void SetExpectedValue(string key, object value)
            {
                lock (_lock)
                {
                    _expectedData[key] = value;
                }
            }

            public bool ValidateData(StorageInstance storage)
            {
                lock (_lock)
                {
                    foreach (var kvp in _expectedData)
                    {
                        if (!storage.TryGet(kvp.Key, out object actualValue))
                        {
                            return false;
                        }

                        if (!CompareValues(kvp.Value, actualValue))
                        {
                            return false;
                        }
                    }
                    return true;
                }
            }

            public int ExpectedDataCount
            {
                get
                {
                    lock (_lock)
                    {
                        return _expectedData.Count;
                    }
                }
            }
        }

        #endregion

        #region MonoBehaviour生命周期

        private void Start()
        {
            if (_runTestsOnStart)
            {
                StartCoroutine(RunAllTestsCoroutine());
            }
        }

        #endregion

        #region 公共测试方法

        /// <summary>
        /// 运行所有测试
        /// </summary>
        [ContextMenu("运行所有测试")]
        public void RunAllTests()
        {
            StartCoroutine(RunAllTestsCoroutine());
        }

        /// <summary>
        /// 运行基础类型测试
        /// </summary>
        [ContextMenu("运行基础类型测试")]
        public void RunBasicTypesTest()
        {
            StartCoroutine(RunBasicTypesTestCoroutine());
        }

        /// <summary>
        /// 运行Unity类型测试
        /// </summary>
        [ContextMenu("运行Unity类型测试")]
        public void RunUnityTypesTest()
        {
            StartCoroutine(RunUnityTypesTestCoroutine());
        }

        /// <summary>
        /// 运行集合类型测试
        /// </summary>
        [ContextMenu("运行集合类型测试")]
        public void RunCollectionTypesTest()
        {
            StartCoroutine(RunCollectionTypesTestCoroutine());
        }

        /// <summary>
        /// 运行null值测试
        /// </summary>
        [ContextMenu("运行null值测试")]
        public void RunNullValueTest()
        {
            StartCoroutine(RunNullValueTestCoroutine());
        }

        /// <summary>
        /// 运行性能测试
        /// </summary>
        [ContextMenu("运行性能测试")]
        public void RunPerformanceTest()
        {
            StartCoroutine(RunPerformanceTestCoroutine());
        }

        /// <summary>
        /// 运行持久化测试
        /// </summary>
        [ContextMenu("运行持久化测试")]
        public void RunPersistenceTest()
        {
            StartCoroutine(RunPersistenceTestCoroutine());
        }

        /// <summary>
        /// 运行高级类型测试
        /// </summary>
        [ContextMenu("运行高级类型测试")]
        public void RunAdvancedTypesTest()
        {
            StartCoroutine(RunAdvancedTypesTestCoroutine());
        }

        /// <summary>
        /// 运行加密测试
        /// </summary>
        [ContextMenu("运行加密测试")]
        public void RunEncryptionTest()
        {
            StartCoroutine(RunEncryptionTestCoroutine());
        }

        [ContextMenu("运行集合类型测试")]
        public void RunCollectionTypesTestCoroutineTest()
        {
            StartCoroutine(RunCollectionTypesTestCoroutine());
        }

        /// <summary>
        /// 运行并发测试
        /// </summary>
        [ContextMenu("运行并发测试")]
        public void RunConcurrencyTests()
        {
            StartCoroutine(RunConcurrencyTestsCoroutine());
        }

        /// <summary>
        /// 运行同一实例并发测试
        /// </summary>
        [ContextMenu("运行同一实例并发测试")]
        public void RunSameInstanceConcurrencyTest()
        {
            StartCoroutine(RunSameInstanceConcurrencyTestCoroutine());
        }

        /// <summary>
        /// 运行不同实例并发测试
        /// </summary>
        [ContextMenu("运行不同实例并发测试")]
        public void RunDifferentInstancesConcurrencyTest()
        {
            StartCoroutine(RunDifferentInstancesConcurrencyTestCoroutine());
        }

        /// <summary>
        /// 运行读写并发测试
        /// </summary>
        [ContextMenu("运行读写并发测试")]
        public void RunReadWriteConcurrencyTest()
        {
            StartCoroutine(RunReadWriteConcurrencyTestCoroutine());
        }

        /// <summary>
        /// 运行TypeWrapper性能测试
        /// </summary>
        [ContextMenu("运行TypeWrapper性能测试")]
        public void RunTypeWrapperPerformanceTest()
        {
            StartCoroutine(RunTypeWrapperPerformanceTestCoroutine());
        }

        #endregion

        #region 测试协程

        /// <summary>
        /// 运行所有测试的协程
        /// </summary>
        private IEnumerator RunAllTestsCoroutine()
        {
            LogTestHeader("=== Storage序列化器全面测试开始 ===");

            // 初始化Storage系统
            StorageManager.Initialize();
            yield return null;

            // 重置测试结果
            _testResults.Clear();
            _totalTests = 0;
            _passedTests = 0;

            // 运行各类测试
            if (_testBasicTypes)
            {
                yield return RunBasicTypesTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testUnityTypes)
            {
                yield return RunUnityTypesTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testCollectionTypes)
            {
                yield return RunCollectionTypesTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testCustomTypes)
            {
                yield return RunCustomTypesTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testNestedTypes)
            {
                yield return RunNestedTypesTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testNullValues)
            {
                yield return RunNullValueTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testBoundaryConditions)
            {
                yield return RunBoundaryConditionsTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testErrorHandling)
            {
                yield return RunErrorHandlingTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testPersistence)
            {
                yield return RunPersistenceTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testAdvancedTypes)
            {
                yield return RunAdvancedTypesTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testEncryption)
            {
                yield return RunEncryptionTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_runPerformanceTests)
            {
                yield return RunPerformanceTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testConcurrency)
            {
                yield return RunConcurrencyTestsCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            // 生成最终报告
            GenerateTestReport();
        }

        /// <summary>
        /// 基础类型测试协程
        /// </summary>
        private IEnumerator RunBasicTypesTestCoroutine()
        {
            LogTestSection("--- 基础类型测试 ---");

            var storage = CreateTestStorageInstance("BasicTypes");
            if (storage == null)
            {
                LogTestError("无法创建测试Storage实例");
                yield break;
            }

            // 测试int类型
            yield return TestBasicType(storage, "testInt", 42, "int类型");
            yield return TestBasicType(storage, "testIntNegative", -123, "负整数");
            yield return TestBasicType(storage, "testIntZero", 0, "零值整数");
            yield return TestBasicType(storage, "testIntMax", int.MaxValue, "最大整数");
            yield return TestBasicType(storage, "testIntMin", int.MinValue, "最小整数");

            // 测试float类型
            yield return TestBasicType(storage, "testFloat", 3.14f, "float类型");
            yield return TestBasicType(storage, "testFloatNegative", -2.71f, "负浮点数");
            yield return TestBasicType(storage, "testFloatZero", 0.0f, "零值浮点数");
            yield return TestBasicType(storage, "testFloatMax", float.MaxValue, "最大浮点数");
            yield return TestBasicType(storage, "testFloatMin", float.MinValue, "最小浮点数");

            // 测试double类型
            yield return TestBasicType(storage, "testDouble", 3.141592653589793, "double类型");
            yield return TestBasicType(storage, "testDoubleNegative", -2.718281828459045, "负双精度浮点数");

            // 测试bool类型
            yield return TestBasicType(storage, "testBoolTrue", true, "布尔真值");
            yield return TestBasicType(storage, "testBoolFalse", false, "布尔假值");

            // 测试string类型
            yield return TestBasicType(storage, "testString", "Hello World", "普通字符串");
            yield return TestBasicType(storage, "testStringEmpty", "", "空字符串");
            yield return TestBasicType(storage, "testStringSpecial", "特殊字符：中文，符号!@#$%^&*()", "特殊字符串");
            yield return TestBasicType(storage, "testStringUnicode", "Unicode: 🎮🚀💻", "Unicode字符串");
            yield return TestBasicType(storage, "testStringJson", "{\"key\": \"value\"}", "JSON格式字符串");

            // 测试其他基础类型
            yield return TestBasicType(storage, "testByte", (byte)255, "byte类型");
            yield return TestBasicType(storage, "testShort", (short)32767, "short类型");
            yield return TestBasicType(storage, "testLong", 9223372036854775807L, "long类型");
            yield return TestBasicType(storage, "testChar", 'A', "char类型");
            yield return TestBasicType(storage, "testDecimal", 123.456789m, "decimal类型");

            StorageManager.DestroyInstance(storage.InstanceId);
            LogTestInfo("基础类型测试完成");
        }

        /// <summary>
        /// Unity类型测试协程
        /// </summary>
        private IEnumerator RunUnityTypesTestCoroutine()
        {
            LogTestSection("--- Unity类型测试 ---");

            var storage = CreateTestStorageInstance("UnityTypes");
            if (storage == null)
            {
                LogTestError("无法创建测试Storage实例");
                yield break;
            }

            // 测试Vector类型
            yield return TestBasicType(storage, "testVector2", new Vector2(1.5f, 2.5f), "Vector2类型");
            yield return TestBasicType(storage, "testVector3", new Vector3(1.0f, 2.0f, 3.0f), "Vector3类型");
            yield return TestBasicType(storage, "testVector4", new Vector4(1.0f, 2.0f, 3.0f, 4.0f), "Vector4类型");
            yield return TestBasicType(storage, "testVector2Int", new Vector2Int(10, 20), "Vector2Int类型");
            yield return TestBasicType(storage, "testVector3Int", new Vector3Int(1, 2, 3), "Vector3Int类型");

            // 测试Quaternion类型
            yield return TestBasicType(storage, "testQuaternion", Quaternion.Euler(45, 90, 180), "Quaternion类型");
            yield return TestBasicType(storage, "testQuaternionIdentity", Quaternion.identity, "Quaternion单位四元数");

            // 测试Color类型
            yield return TestBasicType(storage, "testColor", Color.red, "Color红色");
            yield return TestBasicType(storage, "testColorCustom", new Color(0.5f, 0.3f, 0.8f, 0.9f), "自定义Color");
            yield return TestBasicType(storage, "testColor32", new Color32(255, 128, 64, 255), "Color32类型");

            // 测试Rect类型
            yield return TestBasicType(storage, "testRect", new Rect(10, 20, 100, 200), "Rect类型");
            yield return TestBasicType(storage, "testRectInt", new RectInt(5, 10, 50, 100), "RectInt类型");

            // 测试Bounds类型
            yield return TestBasicType(storage, "testBounds", new Bounds(Vector3.zero, Vector3.one), "Bounds类型");
            yield return TestBasicType(storage, "testBoundsInt", new BoundsInt(0, 0, 0, 10, 10, 10), "BoundsInt类型");

            // 测试Matrix4x4类型
            yield return TestBasicType(storage, "testMatrix", Matrix4x4.identity, "Matrix4x4单位矩阵");
            yield return TestBasicType(storage, "testMatrixTRS", Matrix4x4.TRS(Vector3.one, Quaternion.identity, Vector3.one * 2), "Matrix4x4 TRS矩阵");

            StorageManager.DestroyInstance(storage.InstanceId);
            LogTestInfo("Unity类型测试完成");
        }

        /// <summary>
        /// 集合类型测试协程
        /// </summary>
        private IEnumerator RunCollectionTypesTestCoroutine()
        {
            LogTestSection("--- 集合类型测试 ---");

            var storage = CreateTestStorageInstance("CollectionTypes");
            if (storage == null)
            {
                LogTestError("无法创建测试Storage实例");
                yield break;
            }

            // 测试数组类型
            yield return TestBasicType(storage, "testIntArray", new int[] { 1, 2, 3, 4, 5 }, "int数组");
            yield return TestBasicType(storage, "testFloatArray", new float[] { 1.1f, 2.2f, 3.3f }, "float数组");
            yield return TestBasicType(storage, "testStringArray", new string[] { "apple", "banana", "cherry" }, "string数组");
            yield return TestBasicType(storage, "testVector3Array", new Vector3[] { Vector3.zero, Vector3.one, Vector3.up }, "Vector3数组");

            // 测试空数组
            yield return TestBasicType(storage, "testEmptyIntArray", new int[0], "空int数组");

            // 测试List类型
            var intList = new List<int> { 10, 20, 30, 40, 50 };
            yield return TestBasicType(storage, "testIntList", intList, "int列表");

            var stringList = new List<string> { "Hello", "World", "Unity" };
            yield return TestBasicType(storage, "testStringList", stringList, "string列表");

            var vector3List = new List<Vector3> { Vector3.forward, Vector3.back, Vector3.left, Vector3.right };
            yield return TestBasicType(storage, "testVector3List", vector3List, "Vector3列表");

            // 测试空列表
            yield return TestBasicType(storage, "testEmptyIntList", new List<int>(), "空int列表");

            // 测试Dictionary类型
            var stringIntDict = new Dictionary<string, int>
            {
                { "first", 1 },
                { "second", 2 },
                { "third", 3 }
            };
            yield return TestBasicType(storage, "testStringIntDict", stringIntDict, "string-int字典");

            var intStringDict = new Dictionary<int, string>
            {
                { 1, "one" },
                { 2, "two" },
                { 3, "three" }
            };
            yield return TestBasicType(storage, "testIntStringDict", intStringDict, "int-string字典");

            var stringVector3Dict = new Dictionary<string, Vector3>
            {
                { "origin", Vector3.zero },
                { "up", Vector3.up },
                { "forward", Vector3.forward }
            };
            yield return TestBasicType(storage, "testStringVector3Dict", stringVector3Dict, "string-Vector3字典");

            // 测试空字典
            yield return TestBasicType(storage, "testEmptyStringIntDict", new Dictionary<string, int>(), "空string-int字典");

            StorageManager.DestroyInstance(storage.InstanceId);
            LogTestInfo("集合类型测试完成");
        }

        /// <summary>
        /// 自定义类型测试协程
        /// </summary>
        private IEnumerator RunCustomTypesTestCoroutine()
        {
            LogTestSection("--- 自定义类型测试 ---");

            var storage = CreateTestStorageInstance("CustomTypes");
            if (storage == null)
            {
                LogTestError("无法创建测试Storage实例");
                yield break;
            }

            // 测试简单自定义类
            var simpleClass = new SimpleTestClass(42, 3.14f, "test", true);
            yield return TestBasicType(storage, "testSimpleClass", simpleClass, "简单自定义类");

            // 测试自定义结构体
            var testStruct = new TestStruct(100, new Vector2(5.0f, 10.0f), Color.blue);
            yield return TestBasicType(storage, "testStruct", testStruct, "自定义结构体");

            StorageManager.DestroyInstance(storage.InstanceId);
            LogTestInfo("自定义类型测试完成");
        }

        /// <summary>
        /// 嵌套类型测试协程
        /// </summary>
        private IEnumerator RunNestedTypesTestCoroutine()
        {
            LogTestSection("--- 嵌套类型测试 ---");

            var storage = CreateTestStorageInstance("NestedTypes");
            if (storage == null)
            {
                LogTestError("无法创建测试Storage实例");
                yield break;
            }

            // 创建复杂嵌套对象
            var complexObj = new ComplexTestClass
            {
                simple = new SimpleTestClass(123, 4.56f, "nested", false),
                numbers = new List<int> { 1, 1, 2, 3, 5, 8, 13 },
                floatDict = new Dictionary<string, float>
                {
                    { "pi", 3.14159f },
                    { "e", 2.71828f },
                    { "sqrt2", 1.41421f }
                },
                positions = new Vector3[]
                {
                    new Vector3(1, 0, 0),
                    new Vector3(0, 1, 0),
                    new Vector3(0, 0, 1)
                },
                colors = new Color[]
                {
                    Color.red,
                    Color.green,
                    Color.blue,
                    new Color(1, 1, 1, 0.5f)
                }
            };

            yield return TestBasicType(storage, "testComplexObj", complexObj, "复杂嵌套对象");

            // 测试嵌套集合
            var nestedLists = new List<List<int>>
            {
                new List<int> { 1, 2, 3 },
                new List<int> { 4, 5, 6 },
                new List<int> { 7, 8, 9 }
            };
            yield return TestBasicType(storage, "testNestedLists", nestedLists, "嵌套列表");

            StorageManager.DestroyInstance(storage.InstanceId);
            LogTestInfo("嵌套类型测试完成");
        }

        /// <summary>
        /// null值测试协程
        /// </summary>
        private IEnumerator RunNullValueTestCoroutine()
        {
            LogTestSection("--- null值测试 ---");

            var storage = CreateTestStorageInstance("NullValues");
            if (storage == null)
            {
                LogTestError("无法创建测试Storage实例");
                yield break;
            }

            // 测试null字符串
            yield return TestBasicType<string>(storage, "testNullString", null, "null字符串");

            // 测试null对象
            yield return TestBasicType<SimpleTestClass>(storage, "testNullObject", null, "null对象");

            // 测试null数组
            yield return TestBasicType<int[]>(storage, "testNullArray", null, "null数组");

            // 测试null列表
            yield return TestBasicType<List<int>>(storage, "testNullList", null, "null列表");

            // 测试包含null元素的数组
            var arrayWithNulls = new string[] { "first", null, "third", null };
            yield return TestBasicType(storage, "testArrayWithNulls", arrayWithNulls, "包含null元素的数组");

            StorageManager.DestroyInstance(storage.InstanceId);
            LogTestInfo("null值测试完成");
        }

        /// <summary>
        /// 边界条件测试协程
        /// </summary>
        private IEnumerator RunBoundaryConditionsTestCoroutine()
        {
            LogTestSection("--- 边界条件测试 ---");

            var storage = CreateTestStorageInstance("BoundaryConditions");
            if (storage == null)
            {
                LogTestError("无法创建测试Storage实例");
                yield break;
            }

            // 测试极大集合
            var largeList = new List<int>();
            for (int i = 0; i < 10000; ++i)
            {
                largeList.Add(i);
            }
            yield return TestBasicType(storage, "testLargeList", largeList, "大型列表(10000元素)");

            // 测试很长的字符串
            var longString = new string('A', 10000);
            yield return TestBasicType(storage, "testLongString", longString, "超长字符串(10000字符)");

            // 测试特殊浮点数值
            yield return TestBasicType(storage, "testFloatNaN", float.NaN, "NaN浮点数");
            yield return TestBasicType(storage, "testFloatInfinity", float.PositiveInfinity, "正无穷浮点数");
            yield return TestBasicType(storage, "testFloatNegInfinity", float.NegativeInfinity, "负无穷浮点数");

            // 测试空key（应该失败）
            yield return TestErrorCase(storage, "", 42, "空键测试");

            StorageManager.DestroyInstance(storage.InstanceId);
            LogTestInfo("边界条件测试完成");
        }

        /// <summary>
        /// 错误处理测试协程
        /// </summary>
        private IEnumerator RunErrorHandlingTestCoroutine()
        {
            LogTestSection("--- 错误处理测试 ---");

            var storage = CreateTestStorageInstance("ErrorHandling");
            if (storage == null)
            {
                LogTestError("无法创建测试Storage实例");
                yield break;
            }

            // 测试获取不存在的键
            yield return TestNonExistentKey(storage);

            // 测试类型不匹配
            yield return TestTypeMismatch(storage);

            StorageManager.DestroyInstance(storage.InstanceId);
            LogTestInfo("错误处理测试完成");
        }

        /// <summary>
        /// 性能测试协程
        /// </summary>
        private IEnumerator RunPerformanceTestCoroutine()
        {
            LogTestSection("--- 性能测试 ---");

            var storage = CreateTestStorageInstance("Performance");
            if (storage == null)
            {
                LogTestError("无法创建测试Storage实例");
                yield break;
            }

            // 大量写入操作性能测试
            var startTime = Time.realtimeSinceStartup;
            for (int i = 0; i < _performanceIterations; ++i)
            {
                storage.Set($"perfTest_{i}", i);

                // 每100次操作让出一帧
                if (i % 100 == 0)
                {
                    yield return null;
                }
            }
            var writeTime = Time.realtimeSinceStartup - startTime;

            // 大量读取操作性能测试
            startTime = Time.realtimeSinceStartup;
            for (int i = 0; i < _performanceIterations; ++i)
            {
                storage.TryGet($"perfTest_{i}", out int value);

                // 每100次操作让出一帧
                if (i % 100 == 0)
                {
                    yield return null;
                }
            }
            var readTime = Time.realtimeSinceStartup - startTime;

            LogTestInfo($"性能测试结果：写入{_performanceIterations}次用时{writeTime:F3}秒，读取{_performanceIterations}次用时{readTime:F3}秒");

            StorageManager.DestroyInstance(storage.InstanceId);
            LogTestInfo("性能测试完成");
        }

        /// <summary>
        /// 运行持久化测试的协程
        /// </summary>
        private IEnumerator RunPersistenceTestCoroutine()
        {
            LogTestSection("--- 持久化测试 ---");

            // 第一阶段：写入数据到持久化存储
            var writeStorage = CreatePersistentStorageInstance("PersistenceWrite");
            if (writeStorage == null)
            {
                LogTestError("无法创建写入测试Storage实例");
                yield break;
            }

            // 写入各种类型的测试数据
            LogTestInfo("正在写入持久化测试数据...");

            var testData = new Dictionary<string, object>
            {
                { "persistInt", 12345 },
                { "persistFloat", 3.14159f },
                { "persistString", "持久化测试字符串" },
                { "persistBool", true },
                { "persistVector3", new Vector3(1.5f, 2.5f, 3.5f) },
                { "persistColor", Color.red },
                { "persistIntArray", new int[] { 1, 2, 3, 4, 5 } },
                { "persistStringList", new List<string> { "apple", "banana", "cherry" } },
                { "persistDict", new Dictionary<string, int> { { "one", 1 }, { "two", 2 }, { "three", 3 } } }
            };

            foreach (var kvp in testData)
            {
                var success = writeStorage.Set(kvp.Key, kvp.Value);
                var testResult = new TestResult($"写入持久化数据: {kvp.Key}", success);
                if (!success)
                {
                    testResult.errorMessage = "写入失败";
                }
                AddTestResult(testResult);

                if (_enableDetailedLogging)
                {
                    if (success)
                        LogTestSuccess($"✓ 写入持久化数据: {kvp.Key} = {FormatValue(kvp.Value)}");
                    else
                        LogTestError($"✗ 写入持久化数据失败: {kvp.Key}");
                }

                yield return null;
            }

            // 强制保存到文件
            LogTestInfo("强制保存数据到文件...");
            var saveSuccess = writeStorage.SaveToFileSync();
            var saveResult = new TestResult("强制保存到文件", saveSuccess);
            if (!saveSuccess)
            {
                saveResult.errorMessage = "保存到文件失败";
            }
            AddTestResult(saveResult);

            if (_enableDetailedLogging)
            {
                if (saveSuccess)
                    LogTestSuccess("✓ 数据已成功保存到文件");
                else
                    LogTestError("✗ 保存到文件失败");
            }

            // 销毁第一个存储实例
            StorageManager.DestroyInstance(writeStorage.InstanceId);
            yield return new WaitForSeconds(0.5f);

            // 第二阶段：从持久化存储读取数据
            LogTestInfo("正在从持久化存储读取数据...");
            var readStorage = CreatePersistentStorageInstance("PersistenceWrite"); // 使用相同的文件名读取数据
            if (readStorage == null)
            {
                LogTestError("无法创建读取测试Storage实例");
                yield break;
            }

            // 先从文件加载数据
            var loadSuccess = readStorage.LoadFromFileSync();
            var loadResult = new TestResult("从文件加载数据", loadSuccess);
            if (!loadSuccess)
            {
                loadResult.errorMessage = "从文件加载失败";
            }
            AddTestResult(loadResult);

            if (_enableDetailedLogging)
            {
                if (loadSuccess)
                    LogTestSuccess("✓ 数据已成功从文件加载");
                else
                    LogTestError("✗ 从文件加载失败");
            }

            // 验证读取的数据是否与写入的数据一致
            LogTestInfo("验证持久化数据完整性...");
            foreach (var kvp in testData)
            {
                var testResult = new TestResult($"验证持久化数据: {kvp.Key}", false);
                var startTime = Time.realtimeSinceStartup;

                try
                {
                    bool success = readStorage.TryGet(kvp.Key, out object retrievedValue);

                    if (!success)
                    {
                        testResult.errorMessage = "无法读取数据";
                    }
                    else
                    {
                        // 比较数据
                        bool dataMatches = CompareValues(kvp.Value, retrievedValue);

                        if (!dataMatches)
                        {
                            testResult.errorMessage = $"数据不匹配: 原始值={FormatValue(kvp.Value)}, 读取值={FormatValue(retrievedValue)}";
                        }
                        else
                        {
                            testResult.passed = true;
                            if (_enableDetailedLogging)
                            {
                                LogTestSuccess($"✓ 持久化数据验证通过: {kvp.Key}");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    testResult.errorMessage = $"异常: {ex.Message}";
                }

                testResult.executionTime = Time.realtimeSinceStartup - startTime;
                AddTestResult(testResult);

                if (!testResult.passed)
                {
                    LogTestError($"✗ 持久化数据验证失败: {kvp.Key}: {testResult.errorMessage}");
                }

                yield return null;
            }

            // 测试数据修改和重新保存
            LogTestInfo("测试数据修改和重新保存...");
            readStorage.Set("persistInt", 54321);
            readStorage.Set("persistString", "修改后的持久化字符串");
            readStorage.Set("newPersistKey", "新增的持久化数据");

            var resaveSuccess = readStorage.SaveToFileSync();
            var resaveResult = new TestResult("修改后重新保存", resaveSuccess);
            AddTestResult(resaveResult);

            if (_enableDetailedLogging)
            {
                if (resaveSuccess)
                    LogTestSuccess("✓ 修改后的数据已成功重新保存");
                else
                    LogTestError("✗ 修改后重新保存失败");
            }

            StorageManager.DestroyInstance(readStorage.InstanceId);
            LogTestInfo("持久化测试完成");
        }

        /// <summary>
        /// 运行高级类型测试的协程
        /// </summary>
        private IEnumerator RunAdvancedTypesTestCoroutine()
        {
            LogTestSection("--- 高级类型测试 ---");

            var storage = CreateTestStorageInstance("AdvancedTypes");
            if (storage == null)
            {
                LogTestError("无法创建测试Storage实例");
                yield break;
            }

            // 测试高级类型
            yield return TestAdvancedType(storage, "testInheritanceA", new InheritanceTestA("id1", 42, 3.14f), "继承测试类A");
            yield return TestAdvancedType(storage, "testInheritanceB", new InheritanceTestB("id2", Vector3.one), "继承测试类B");
            yield return TestAdvancedType(storage, "testGenericClass", new GenericTestClass<int>(42), "泛型测试类");
            yield return TestAdvancedType(storage, "testDeepNested", DeepNestedTestClass.CreateNestedStructure(5), "深度嵌套测试类");
            yield return TestAdvancedType(storage, "testMultiDimensional", new MultiDimensionalTestClass
            {
                matrix2D = new int[,] { { 1, 2 }, { 3, 4 } },
                matrix3D = new int[,,] { { { 1, 2, 3 }, { 4, 5, 6 }, { 7, 8, 9 } }, { { 10, 11, 12 }, { 13, 14, 15 }, { 16, 17, 18 } } },
                jaggedArray = new string[][] { new string[] { "a", "b" }, new string[] { "c", "d", "e" } },
                listOfArrays = new List<int[]> { new int[] { 1, 2, 3 }, new int[] { 4, 5, 6 } },
                dictOfArrays = new Dictionary<string, float[]> { { "array1", new float[] { 1.1f, 2.2f } }, { "array2", new float[] { 3.3f, 4.4f, 5.5f } } }
            }, "多维数组测试类");

            StorageManager.DestroyInstance(storage.InstanceId);
            LogTestInfo("高级类型测试完成");
        }

        /// <summary>
        /// 运行加密测试的协程
        /// </summary>
        private IEnumerator RunEncryptionTestCoroutine()
        {
            LogTestSection("--- 加密测试 ---");

            // 测试AES加密
            LogTestInfo("测试AES加密...");
            var aesStorage = CreatePersistentStorageInstance("EncryptionAES", StorageSettings.EncryptionType.AES);
            if (aesStorage == null)
            {
                LogTestError("无法创建AES加密测试Storage实例");
                yield break;
            }

            yield return TestEncryptionForStorage(aesStorage, "AES");
            StorageManager.DestroyInstance(aesStorage.InstanceId);
            yield return new WaitForSeconds(0.1f);

            // 测试XOR加密
            LogTestInfo("测试XOR加密...");
            var xorStorage = CreatePersistentStorageInstance("EncryptionXOR", StorageSettings.EncryptionType.XOR);
            if (xorStorage == null)
            {
                LogTestError("无法创建XOR加密测试Storage实例");
                yield break;
            }

            yield return TestEncryptionForStorage(xorStorage, "XOR");
            StorageManager.DestroyInstance(xorStorage.InstanceId);

            LogTestInfo("加密测试完成");
        }

        /// <summary>
        /// 为特定存储实例测试加密功能
        /// </summary>
        private IEnumerator TestEncryptionForStorage(StorageInstance storage, string encryptionType)
        {
            LogTestInfo($"开始{encryptionType}加密测试...");

            var encryptedData = new Dictionary<string, object>
            {
                { "encryptedInt", 999 },
                { "encryptedString", "这是加密的字符串内容" },
                { "encryptedFloat", 123.456f },
                { "encryptedBool", true },
                { "encryptedVector3", new Vector3(10f, 20f, 30f) },
                { "encryptedArray", new int[] { 10, 20, 30, 40, 50 } },
                { "encryptedList", new List<string> { "加密", "测试", "数据" } },
                { "encryptedDict", new Dictionary<string, float> { { "加密键1", 1.1f }, { "加密键2", 2.2f } } }
            };

            // 写入加密数据
            foreach (var kvp in encryptedData)
            {
                var success = storage.Set(kvp.Key, kvp.Value);
                var testResult = new TestResult($"{encryptionType}加密写入: {kvp.Key}", success);
                if (!success)
                {
                    testResult.errorMessage = "写入失败";
                }
                AddTestResult(testResult);

                if (_enableDetailedLogging)
                {
                    if (success)
                        LogTestSuccess($"✓ {encryptionType}加密写入成功: {kvp.Key}");
                    else
                        LogTestError($"✗ {encryptionType}加密写入失败: {kvp.Key}");
                }

                yield return null;
            }

            // 保存加密数据
            var saveSuccess = storage.SaveToFileSync();
            var saveResult = new TestResult($"{encryptionType}加密保存到文件", saveSuccess);
            AddTestResult(saveResult);

            if (_enableDetailedLogging)
            {
                if (saveSuccess)
                    LogTestSuccess($"✓ {encryptionType}加密数据已保存到文件");
                else
                    LogTestError($"✗ {encryptionType}加密数据保存失败");
            }

            // 验证加密数据的读取
            foreach (var kvp in encryptedData)
            {
                var testResult = new TestResult($"{encryptionType}加密验证: {kvp.Key}", false);
                var startTime = Time.realtimeSinceStartup;

                try
                {
                    bool success = storage.TryGet(kvp.Key, out object retrievedValue);

                    if (!success)
                    {
                        testResult.errorMessage = "无法读取加密数据";
                    }
                    else
                    {
                        bool dataMatches = CompareValues(kvp.Value, retrievedValue);

                        if (!dataMatches)
                        {
                            testResult.errorMessage = $"加密数据不匹配: 原始值={FormatValue(kvp.Value)}, 读取值={FormatValue(retrievedValue)}";
                        }
                        else
                        {
                            testResult.passed = true;
                            if (_enableDetailedLogging)
                            {
                                LogTestSuccess($"✓ {encryptionType}加密数据验证通过: {kvp.Key}");
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    testResult.errorMessage = $"异常: {ex.Message}";
                }

                testResult.executionTime = Time.realtimeSinceStartup - startTime;
                AddTestResult(testResult);

                if (!testResult.passed)
                {
                    LogTestError($"✗ {encryptionType}加密数据验证失败: {kvp.Key}: {testResult.errorMessage}");
                }

                yield return null;
            }

            LogTestInfo($"{encryptionType}加密测试完成");
        }

        #endregion

        #region 辅助测试方法

        /// <summary>
        /// 创建测试用的Storage实例
        /// </summary>
        private StorageInstance CreateTestStorageInstance(string testName)
        {
            try
            {
                var settings = new StorageSettings($"Test_{testName}.json")
                {
                    Location = StorageSettings.StorageLocation.Memory,
                    Encryption = StorageSettings.EncryptionType.None,
                    PrettyPrint = false
                };

                return StorageManager.CreateInstance($"Test_{testName}", settings);
            }
            catch (Exception ex)
            {
                LogTestError($"创建Storage实例失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建持久化测试用的Storage实例
        /// </summary>
        private StorageInstance CreatePersistentStorageInstance(string testName, StorageSettings.EncryptionType encryption = StorageSettings.EncryptionType.None)
        {
            try
            {
                var settings = new StorageSettings($"Test_{testName}.json")
                {
                    Location = StorageSettings.StorageLocation.File,
                    Directory = StorageSettings.DirectoryType.PersistentDataPath,
                    Encryption = encryption,
                    PrettyPrint = true
                };

                return StorageManager.CreateInstance($"Test_{testName}", settings);
            }
            catch (Exception ex)
            {
                LogTestError($"创建持久化Storage实例失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 测试基础类型的往返序列化
        /// </summary>
        private IEnumerator TestBasicType<T>(StorageInstance storage, string key, T originalValue, string testDescription)
        {
            var testResult = new TestResult($"{testDescription}({typeof(T).Name})", false);
            var startTime = Time.realtimeSinceStartup;

            try
            {
                // 写入数据
                storage.Set(key, originalValue);

                // 读取数据
                bool success = storage.TryGet(key, out T retrievedValue);

                if (!success)
                {
                    testResult.errorMessage = "无法读取数据";
                }
                else if (!CompareValues(originalValue, retrievedValue))
                {
                    testResult.errorMessage = $"数据不匹配: 原始值={FormatValue(originalValue)}, 读取值={FormatValue(retrievedValue)}";
                }
                else
                {
                    testResult.passed = true;
                    if (_enableDetailedLogging)
                    {
                        LogTestSuccess($"✓ {testDescription}: {FormatValue(originalValue)}");
                    }
                }
            }
            catch (Exception ex)
            {
                testResult.errorMessage = $"异常: {ex.Message}";
            }

            testResult.executionTime = Time.realtimeSinceStartup - startTime;
            AddTestResult(testResult);

            if (!testResult.passed)
            {
                LogTestError($"✗ {testDescription}: {testResult.errorMessage}");
            }

            yield return null;
        }

        /// <summary>
        /// 测试错误情况
        /// </summary>
        private IEnumerator TestErrorCase<T>(StorageInstance storage, string key, T value, string testDescription)
        {
            var testResult = new TestResult($"{testDescription}(错误处理)", false);
            var startTime = Time.realtimeSinceStartup;

            bool success = storage.Set(key, value);
            // 预期中的错误
            if (success)
            {
                testResult.errorMessage = "应该产生错误但没有";
                if (_enableDetailedLogging)
                {
                    LogTestError($"✗ {testDescription}: {testResult.errorMessage}");
                }
            }
            else
            {
                testResult.passed = true;
                if (_enableDetailedLogging)
                {
                    LogTestSuccess($"✓ {testDescription}: 正确处理了错误情况");
                }
            }

            testResult.executionTime = Time.realtimeSinceStartup - startTime;
            AddTestResult(testResult);

            yield return null;
        }

        /// <summary>
        /// 测试获取不存在的键
        /// </summary>
        private IEnumerator TestNonExistentKey(StorageInstance storage)
        {
            var testResult = new TestResult("获取不存在的键", false);
            var startTime = Time.realtimeSinceStartup;

            try
            {
                bool success = storage.TryGet("nonExistentKey", out int value);

                if (!success && value == 0)
                {
                    testResult.passed = true;
                    if (_enableDetailedLogging)
                    {
                        LogTestSuccess("✓ 正确处理了不存在的键");
                    }
                }
                else
                {
                    testResult.errorMessage = "应该返回false但返回了true，或默认值不正确";
                }
            }
            catch (Exception ex)
            {
                testResult.errorMessage = $"异常: {ex.Message}";
            }

            testResult.executionTime = Time.realtimeSinceStartup - startTime;
            AddTestResult(testResult);

            if (!testResult.passed)
            {
                LogTestError($"✗ 获取不存在的键测试: {testResult.errorMessage}");
            }

            yield return null;
        }

        /// <summary>
        /// 测试类型不匹配
        /// </summary>
        private IEnumerator TestTypeMismatch(StorageInstance storage)
        {
            var testResult = new TestResult("类型不匹配处理", false);
            var startTime = Time.realtimeSinceStartup;

            try
            {
                // 存储为int类型
                storage.Set("typeMismatchTest", 42);

                // 尝试以string类型读取
                bool success = storage.TryGet("typeMismatchTest", out string stringValue);

                if (success && stringValue == "42")
                {
                    testResult.passed = true;
                    if (_enableDetailedLogging)
                    {
                        LogTestSuccess("✓ 正确处理了类型转换");
                    }
                }
                else
                {
                    testResult.errorMessage = $"类型转换失败: success={success}, value={stringValue}";
                }
            }
            catch (Exception ex)
            {
                testResult.errorMessage = $"异常: {ex.Message}";
            }

            testResult.executionTime = Time.realtimeSinceStartup - startTime;
            AddTestResult(testResult);

            if (!testResult.passed)
            {
                LogTestError($"✗ 类型不匹配测试: {testResult.errorMessage}");
            }

            yield return null;
        }

        /// <summary>
        /// 添加测试结果
        /// </summary>
        private void AddTestResult(TestResult result)
        {
            _testResults.Add(result);
            _totalTests++;
            if (result.passed)
            {
                _passedTests++;
            }
        }

        /// <summary>
        /// 格式化值用于显示
        /// </summary>
        private static string FormatValue<T>(T value)
        {
            if (value == null)
                return "null";

            if (value is string str)
                return $"\"{str}\"";

            if (value is ICollection collection)
                return $"{typeof(T).Name}[{collection.Count}]";

            return value.ToString();
        }

        /// <summary>
        /// 测试高级类型
        /// </summary>
        private IEnumerator TestAdvancedType(StorageInstance storage, string key, object originalValue, string testDescription)
        {
            var testResult = new TestResult($"{testDescription}({originalValue.GetType().Name})", false);
            var startTime = Time.realtimeSinceStartup;

            try
            {
                // 写入数据
                storage.Set(key, originalValue);

                // 读取数据
                bool success = storage.TryGet(key, out object retrievedValue);

                if (!success)
                {
                    testResult.errorMessage = "无法读取数据";
                }
                else if (!CompareValues(originalValue, retrievedValue))
                {
                    testResult.errorMessage = $"数据不匹配: 原始值={FormatValue(originalValue)}, 读取值={FormatValue(retrievedValue)}";
                }
                else
                {
                    testResult.passed = true;
                    if (_enableDetailedLogging)
                    {
                        LogTestSuccess($"✓ {testDescription}: {FormatValue(originalValue)}");
                    }
                }
            }
            catch (Exception ex)
            {
                testResult.errorMessage = $"异常: {ex.Message}";
            }

            testResult.executionTime = Time.realtimeSinceStartup - startTime;
            AddTestResult(testResult);

            if (!testResult.passed)
            {
                LogTestError($"✗ {testDescription}: {testResult.errorMessage}");
            }

            yield return null;
        }

        /// <summary>
        /// 比较两个值是否相等（支持集合类型）
        /// </summary>
        private static bool CompareValues(object original, object retrieved)
        {
            if (original == null && retrieved == null) return true;
            if (original == null || retrieved == null) return false;

            // 类型不同直接返回false
            if (original.GetType() != retrieved.GetType()) return false;

            // 使用Equals比较
            if (original.Equals(retrieved)) return true;

            // 对于集合类型进行特殊处理
            if (original is Array originalArray && retrieved is Array retrievedArray)
            {
                return CompareArrays(originalArray, retrievedArray);
            }

            if (original is IList originalList && retrieved is IList retrievedList)
            {
                return CompareLists(originalList, retrievedList);
            }

            if (original is IDictionary originalDict && retrieved is IDictionary retrievedDict)
            {
                return CompareDictionaries(originalDict, retrievedDict);
            }

            return false;
        }

        /// <summary>
        /// 比较两个数组
        /// </summary>
        private static bool CompareArrays(Array a, Array b)
        {
            if (a.Length != b.Length) return false;

            for (int i = 0; i < a.Length; ++i)
            {
                if (!CompareValues(a.GetValue(i), b.GetValue(i))) return false;
            }

            return true;
        }

        /// <summary>
        /// 比较两个列表
        /// </summary>
        private static bool CompareLists(IList a, IList b)
        {
            if (a.Count != b.Count) return false;

            for (int i = 0; i < a.Count; ++i)
            {
                if (!CompareValues(a[i], b[i])) return false;
            }

            return true;
        }

        /// <summary>
        /// 比较两个字典
        /// </summary>
        private static bool CompareDictionaries(IDictionary a, IDictionary b)
        {
            if (a.Count != b.Count) return false;

            foreach (var key in a.Keys)
            {
                if (!b.Contains(key)) return false;
                if (!CompareValues(a[key], b[key])) return false;
            }

            return true;
        }

        #endregion

        #region 日志方法

        /// <summary>
        /// 记录测试标题
        /// </summary>
        private void LogTestHeader(string message)
        {
            UnityEngine.Debug.Log($"<color=cyan><b>{message}</b></color>");
        }

        /// <summary>
        /// 记录测试分节
        /// </summary>
        private void LogTestSection(string message)
        {
            UnityEngine.Debug.Log($"<color=yellow><b>{message}</b></color>");
        }

        /// <summary>
        /// 记录测试信息
        /// </summary>
        private void LogTestInfo(string message)
        {
            UnityEngine.Debug.Log($"<color=white>{message}</color>");
        }

        /// <summary>
        /// 记录测试成功
        /// </summary>
        private void LogTestSuccess(string message)
        {
            UnityEngine.Debug.Log($"<color=green>{message}</color>");
        }

        /// <summary>
        /// 记录测试错误
        /// </summary>
        private void LogTestError(string message)
        {
            UnityEngine.Debug.LogError($"<color=red>{message}</color>");
        }

        #endregion

        #region 报告生成

        /// <summary>
        /// 生成测试报告
        /// </summary>
        private void GenerateTestReport()
        {
            LogTestHeader("=== 测试报告 ===");

            int failedTests = _totalTests - _passedTests;
            float successRate = _totalTests > 0 ? (_passedTests / (float)_totalTests) * 100f : 0f;

            LogTestInfo($"总测试数: {_totalTests}");
            LogTestInfo($"通过测试: {_passedTests}");
            LogTestInfo($"失败测试: {failedTests}");
            LogTestInfo($"成功率: {successRate:F1}%");

            if (failedTests > 0)
            {
                LogTestSection("--- 失败的测试 ---");
                foreach (var result in _testResults)
                {
                    if (!result.passed)
                    {
                        LogTestError($"✗ {result.testName}: {result.errorMessage}");
                    }
                }
            }

            // 性能统计
            float totalTime = 0f;
            foreach (var result in _testResults)
            {
                totalTime += result.executionTime;
            }

            LogTestSection("--- 性能统计 ---");
            LogTestInfo($"总执行时间: {totalTime:F3}秒");
            LogTestInfo($"平均每个测试: {(totalTime / _totalTests):F4}秒");

            // 最终结果
            if (failedTests == 0)
            {
                LogTestSuccess("🎉 所有测试都通过了！");
            }
            else
            {
                LogTestError($"❌ 有 {failedTests} 个测试失败");
            }

            LogTestHeader("=== Storage序列化器测试完成 ===");
        }

        #endregion

        #region 并发测试辅助方法

        /// <summary>
        /// 添加并发测试结果
        /// </summary>
        /// <param name="result">测试结果</param>
        private void AddConcurrentTestResult(ConcurrentTestResult result)
        {
            var testResult = new TestResult(result.testName, result.passed, result.errorMessage, result.executionTime);
            AddTestResult(testResult);
        }

        /// <summary>
        /// 记录并发测试结果详情
        /// </summary>
        /// <param name="result">测试结果</param>
        private void LogConcurrentTestResult(ConcurrentTestResult result)
        {
            if (result.passed)
            {
                LogTestSuccess($"✓ {result.testName}");
            }
            else
            {
                LogTestError($"✗ {result.testName}: {result.errorMessage}");
            }

            LogTestInfo($"  线程数: {result.threadCount}");
            LogTestInfo($"  每线程操作数: {result.operationsPerThread}");
            LogTestInfo($"  总操作数: {result.totalOperations}");
            LogTestInfo($"  成功操作数: {result.successfulOperations}");
            LogTestInfo($"  失败操作数: {result.failedOperations}");
            LogTestInfo($"  执行时间: {result.executionTime:F3}秒");
            LogTestInfo($"  操作速度: {result.operationsPerSecond:F0}操作/秒");
            LogTestInfo($"  内存使用: {result.memoryUsedBytes / 1024.0:F1}KB");
        }

        /// <summary>
        /// 创建用于并发测试的存储实例
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <returns>存储实例</returns>
        private StorageInstance CreateConcurrentTestStorageInstance(string instanceId)
        {
            var settings = new StorageSettings
            {
                FilePath = $"ConcurrentTest_{instanceId}",
                Location = StorageSettings.StorageLocation.File,
                PrettyPrint = false,
                Encryption = StorageSettings.EncryptionType.None
            };

            return StorageManager.CreateInstance(instanceId, settings);
        }

        /// <summary>
        /// 创建用于并发文件测试的持久化存储实例
        /// </summary>
        /// <param name="instanceId">实例ID</param>
        /// <returns>存储实例</returns>
        private StorageInstance CreateConcurrentPersistentStorageInstance(string instanceId)
        {
            var settings = new StorageSettings
            {
                FilePath = $"ConcurrentPersistentTest_{instanceId}",
                Location = StorageSettings.StorageLocation.File,
                PrettyPrint = false,
                Encryption = StorageSettings.EncryptionType.None
            };

            return StorageManager.CreateInstance(instanceId, settings);
        }

        #endregion

        #region 并发测试协程

        /// <summary>
        /// 运行所有并发测试的协程
        /// </summary>
        private IEnumerator RunConcurrencyTestsCoroutine()
        {
            LogTestSection("=== 并发测试开始 ===");

            if (_testSameInstanceConcurrency)
            {
                yield return RunSameInstanceConcurrencyTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testDifferentInstancesConcurrency)
            {
                yield return RunDifferentInstancesConcurrencyTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testReadWriteConcurrency)
            {
                yield return RunReadWriteConcurrencyTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testCacheConcurrency)
            {
                yield return RunCacheConcurrencyTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testFileOperationConcurrency)
            {
                yield return RunFileOperationConcurrencyTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            if (_testTypeWrapperPerformance)
            {
                yield return RunTypeWrapperPerformanceTestCoroutine();
                yield return new WaitForSeconds(0.1f);
            }

            LogTestInfo("并发测试完成");
        }

        /// <summary>
        /// 同一实例并发测试协程
        /// 测试多个线程同时对同一个StorageInstance进行读写操作的安全性
        /// </summary>
        private IEnumerator RunSameInstanceConcurrencyTestCoroutine()
        {
            LogTestSection("--- 同一实例并发测试 ---");

            var storage = CreateConcurrentTestStorageInstance("SameInstanceConcurrency");
            if (storage == null)
            {
                LogTestError("无法创建测试Storage实例");
                yield break;
            }

            var testResult = new ConcurrentTestResult("同一实例并发读写测试")
            {
                threadCount = _concurrentThreadCount,
                operationsPerThread = _concurrentOperationsPerThread,
                totalOperations = _concurrentThreadCount * _concurrentOperationsPerThread
            };

            var counter = new ThreadSafeCounter();
            var exceptionCollector = new ConcurrentExceptionCollector();
            var validator = new ConcurrentDataValidator();

            // 预设期望的数据用于验证
            for (int i = 0; i < _concurrentOperationsPerThread; ++i)
            {
                validator.SetExpectedValue($"thread_final_{i}", i * _concurrentThreadCount);
            }

            var startTime = Time.realtimeSinceStartup;
            var memoryBefore = GC.GetTotalMemory(false);

            // 创建并启动多个线程进行并发操作
            var tasks = new Task[_concurrentThreadCount];
            var countdown = new CountdownEvent(_concurrentThreadCount);

            for (int threadId = 0; threadId < _concurrentThreadCount; ++threadId)
            {
                int capturedThreadId = threadId;
                tasks[threadId] = Task.Run(() =>
                {
                    try
                    {
                        // 每个线程执行指定数量的操作
                        for (int i = 0; i < _concurrentOperationsPerThread; ++i)
                        {
                            try
                            {
                                // 写入操作
                                string writeKey = $"thread_{capturedThreadId}_op_{i}";
                                int writeValue = capturedThreadId * 1000 + i;
                                bool writeSuccess = storage.Set(writeKey, writeValue);

                                if (writeSuccess)
                                {
                                    counter.IncrementSuccess();
                                }
                                else
                                {
                                    counter.IncrementFailure();
                                }

                                // 读取操作
                                if (storage.TryGet(writeKey, out int readValue))
                                {
                                    if (readValue == writeValue)
                                    {
                                        counter.IncrementSuccess();
                                    }
                                    else
                                    {
                                        counter.IncrementFailure();
                                    }
                                }
                                else
                                {
                                    counter.IncrementFailure();
                                }

                                // 累加操作（测试数据竞争）
                                string accumulateKey = $"thread_final_{i}";
                                if (storage.TryGet(accumulateKey, out int currentValue))
                                {
                                    storage.Set(accumulateKey, currentValue + capturedThreadId);
                                }
                                else
                                {
                                    storage.Set(accumulateKey, capturedThreadId);
                                }
                            }
                            catch (Exception ex)
                            {
                                exceptionCollector.AddException(ex);
                                counter.IncrementFailure();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        exceptionCollector.AddException(ex);
                    }
                    finally
                    {
                        countdown.Signal();
                    }
                });
            }

            // 等待所有线程完成，但设置超时
            bool completed = false;
            float timeoutTime = Time.realtimeSinceStartup + _concurrentTestTimeout;

            while (!completed && Time.realtimeSinceStartup < timeoutTime)
            {
                completed = countdown.IsSet;
                yield return null;
            }

            var executionTime = Time.realtimeSinceStartup - startTime;
            var memoryAfter = GC.GetTotalMemory(false);

            // 收集测试结果
            testResult.executionTime = executionTime;
            testResult.successfulOperations = counter.SuccessCount;
            testResult.failedOperations = counter.FailureCount;
            testResult.operationsPerSecond = testResult.totalOperations / executionTime;
            testResult.memoryUsedBytes = memoryAfter - memoryBefore;

            if (completed && !exceptionCollector.HasExceptions)
            {
                // 验证数据完整性
                bool dataValid = validator.ValidateData(storage);
                testResult.passed = dataValid;

                if (!dataValid)
                {
                    testResult.errorMessage = "数据完整性验证失败";
                }
            }
            else
            {
                testResult.passed = false;
                if (!completed)
                {
                    testResult.errorMessage = "测试超时";
                }
                else
                {
                    var exceptions = exceptionCollector.GetAllExceptions();
                    testResult.errorMessage = $"发生{exceptions.Count}个异常: {exceptions[0].Message}";
                }
            }

            // 记录测试结果
            AddConcurrentTestResult(testResult);

            if (_enableConcurrentDetailedLogging)
            {
                LogConcurrentTestResult(testResult);
            }

            StorageManager.DestroyInstance(storage.InstanceId);
            LogTestInfo("同一实例并发测试完成");
        }

        /// <summary>
        /// 不同实例并发测试协程
        /// 测试多个线程同时访问不同StorageInstance的独立性
        /// </summary>
        private IEnumerator RunDifferentInstancesConcurrencyTestCoroutine()
        {
            LogTestSection("--- 不同实例并发测试 ---");

            var testResult = new ConcurrentTestResult("不同实例并发访问测试")
            {
                threadCount = _concurrentThreadCount,
                operationsPerThread = _concurrentOperationsPerThread,
                totalOperations = _concurrentThreadCount * _concurrentOperationsPerThread
            };

            var counter = new ThreadSafeCounter();
            var exceptionCollector = new ConcurrentExceptionCollector();
            var storageInstances = new StorageInstance[_concurrentThreadCount];

            // 为每个线程创建独立的存储实例
            for (int i = 0; i < _concurrentThreadCount; ++i)
            {
                storageInstances[i] = CreateConcurrentTestStorageInstance($"DifferentInstance_{i}");
                if (storageInstances[i] == null)
                {
                    LogTestError($"无法创建存储实例 {i}");
                    yield break;
                }
            }

            var startTime = Time.realtimeSinceStartup;
            var memoryBefore = GC.GetTotalMemory(false);

            // 创建并启动多个线程，每个线程访问不同的存储实例
            var tasks = new Task[_concurrentThreadCount];
            var countdown = new CountdownEvent(_concurrentThreadCount);

            for (int threadId = 0; threadId < _concurrentThreadCount; ++threadId)
            {
                int capturedThreadId = threadId;
                StorageInstance capturedStorage = storageInstances[threadId];

                tasks[threadId] = Task.Run(() =>
                {
                    try
                    {
                        // 每个线程在自己的存储实例上执行操作
                        for (int i = 0; i < _concurrentOperationsPerThread; ++i)
                        {
                            try
                            {
                                // 写入操作
                                string key = $"instance_{capturedThreadId}_data_{i}";
                                var value = new
                                {
                                    threadId = capturedThreadId,
                                    operationId = i,
                                    timestamp = DateTime.Now.Ticks,
                                    data = $"Thread{capturedThreadId}_Op{i}_Data"
                                };

                                bool writeSuccess = capturedStorage.Set(key, value);
                                if (writeSuccess)
                                {
                                    counter.IncrementSuccess();
                                }
                                else
                                {
                                    counter.IncrementFailure();
                                }

                                // 读取验证
                                if (capturedStorage.TryGet(key, out object readValue))
                                {
                                    counter.IncrementSuccess();
                                }
                                else
                                {
                                    counter.IncrementFailure();
                                }

                                // 测试不同类型的数据
                                capturedStorage.Set($"int_{i}", i);
                                capturedStorage.Set($"float_{i}", i * 3.14f);
                                capturedStorage.Set($"string_{i}", $"String_{capturedThreadId}_{i}");
                                capturedStorage.Set($"bool_{i}", i % 2 == 0);

                                counter.IncrementSuccess();
                            }
                            catch (Exception ex)
                            {
                                exceptionCollector.AddException(ex);
                                counter.IncrementFailure();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        exceptionCollector.AddException(ex);
                    }
                    finally
                    {
                        countdown.Signal();
                    }
                });
            }

            // 等待所有线程完成
            bool completed = false;
            float timeoutTime = Time.realtimeSinceStartup + _concurrentTestTimeout;

            while (!completed && Time.realtimeSinceStartup < timeoutTime)
            {
                completed = countdown.IsSet;
                yield return null;
            }

            var executionTime = Time.realtimeSinceStartup - startTime;
            var memoryAfter = GC.GetTotalMemory(false);

            // 收集测试结果
            testResult.executionTime = executionTime;
            testResult.successfulOperations = counter.SuccessCount;
            testResult.failedOperations = counter.FailureCount;
            testResult.operationsPerSecond = testResult.totalOperations / executionTime;
            testResult.memoryUsedBytes = memoryAfter - memoryBefore;

            if (completed && !exceptionCollector.HasExceptions)
            {
                // 验证每个实例的数据独立性
                bool dataValid = true;
                for (int i = 0; i < _concurrentThreadCount; ++i)
                {
                    var storage = storageInstances[i];
                    var keys = storage.GetAllKeys();

                    // 验证数据数量
                    int expectedKeyCount = _concurrentOperationsPerThread * 5; // 每次操作写入5个键
                    if (keys.Count != expectedKeyCount)
                    {
                        dataValid = false;
                        testResult.errorMessage = $"实例{i}数据数量不匹配: 期望{expectedKeyCount}, 实际{keys.Count}";
                        break;
                    }

                    // 验证数据内容
                    for (int j = 0; j < _concurrentOperationsPerThread; ++j)
                    {
                        if (!storage.TryGet($"int_{j}", out int intValue) || intValue != j)
                        {
                            dataValid = false;
                            testResult.errorMessage = $"实例{i}整数数据验证失败";
                            break;
                        }
                    }

                    if (!dataValid) break;
                }

                testResult.passed = dataValid;
            }
            else
            {
                testResult.passed = false;
                if (!completed)
                {
                    testResult.errorMessage = "测试超时";
                }
                else
                {
                    var exceptions = exceptionCollector.GetAllExceptions();
                    testResult.errorMessage = $"发生{exceptions.Count}个异常: {exceptions[0].Message}";
                }
            }

            // 记录测试结果
            AddConcurrentTestResult(testResult);

            if (_enableConcurrentDetailedLogging)
            {
                LogConcurrentTestResult(testResult);
            }

            // 清理所有存储实例
            for (int i = 0; i < _concurrentThreadCount; ++i)
            {
                if (storageInstances[i] != null)
                {
                    StorageManager.DestroyInstance(storageInstances[i].InstanceId);
                }
            }

            LogTestInfo("不同实例并发测试完成");
        }

        /// <summary>
        /// 读写并发测试协程
        /// 测试ReaderWriterLockSlim的正确使用，验证读写操作的互斥性
        /// </summary>
        private IEnumerator RunReadWriteConcurrencyTestCoroutine()
        {
            LogTestSection("--- 读写并发测试 ---");

            var storage = CreateConcurrentPersistentStorageInstance("ReadWriteConcurrency");
            if (storage == null)
            {
                LogTestError("无法创建测试Storage实例");
                yield break;
            }

            // 预先写入一些测试数据
            for (int i = 0; i < 100; ++i)
            {
                storage.Set($"initial_data_{i}", $"InitialValue_{i}");
            }

            var testResult = new ConcurrentTestResult("读写并发测试")
            {
                threadCount = _concurrentThreadCount,
                operationsPerThread = _concurrentOperationsPerThread / 2, // 减少操作数以便观察读写锁效果
                totalOperations = _concurrentThreadCount * (_concurrentOperationsPerThread / 2)
            };

            var counter = new ThreadSafeCounter();
            var exceptionCollector = new ConcurrentExceptionCollector();

            var startTime = Time.realtimeSinceStartup;
            var memoryBefore = GC.GetTotalMemory(false);

            // 创建读写混合的并发任务
            var tasks = new Task[_concurrentThreadCount];
            var countdown = new CountdownEvent(_concurrentThreadCount);

            for (int threadId = 0; threadId < _concurrentThreadCount; ++threadId)
            {
                int capturedThreadId = threadId;
                bool isWriter = threadId % 3 == 0; // 每3个线程中有1个写线程

                tasks[threadId] = Task.Run(() =>
                {
                    try
                    {
                        for (int i = 0; i < testResult.operationsPerThread; ++i)
                        {
                            try
                            {
                                if (isWriter)
                                {
                                    // 写操作：保存到文件
                                    string key = $"writer_{capturedThreadId}_op_{i}";
                                    string value = $"WriterData_{capturedThreadId}_{i}_{DateTime.Now.Ticks}";

                                    storage.Set(key, value);

                                    // 定期触发文件保存操作
                                    if (i % 10 == 0)
                                    {
                                        storage.SaveToFileSync();
                                    }

                                    counter.IncrementSuccess();
                                }
                                else
                                {
                                    // 读操作：从文件加载或从缓存读取
                                    if (i % 20 == 0)
                                    {
                                        // 定期触发文件加载操作
                                        storage.LoadFromFileSync();
                                    }

                                    // 读取现有数据
                                    string readKey = $"initial_data_{i % 100}";
                                    if (storage.TryGet(readKey, out string readValue))
                                    {
                                        counter.IncrementSuccess();
                                    }
                                    else
                                    {
                                        counter.IncrementFailure();
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                exceptionCollector.AddException(ex);
                                counter.IncrementFailure();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        exceptionCollector.AddException(ex);
                    }
                    finally
                    {
                        countdown.Signal();
                    }
                });
            }

            // 等待所有线程完成
            bool completed = false;
            float timeoutTime = Time.realtimeSinceStartup + _concurrentTestTimeout;

            while (!completed && Time.realtimeSinceStartup < timeoutTime)
            {
                completed = countdown.IsSet;
                yield return null;
            }

            var executionTime = Time.realtimeSinceStartup - startTime;
            var memoryAfter = GC.GetTotalMemory(false);

            // 收集测试结果
            testResult.executionTime = executionTime;
            testResult.successfulOperations = counter.SuccessCount;
            testResult.failedOperations = counter.FailureCount;
            testResult.operationsPerSecond = testResult.totalOperations / executionTime;
            testResult.memoryUsedBytes = memoryAfter - memoryBefore;

            if (completed && !exceptionCollector.HasExceptions)
            {
                testResult.passed = true;
            }
            else
            {
                testResult.passed = false;
                if (!completed)
                {
                    testResult.errorMessage = "测试超时";
                }
                else
                {
                    var exceptions = exceptionCollector.GetAllExceptions();
                    testResult.errorMessage = $"发生{exceptions.Count}个异常: {exceptions[0].Message}";
                }
            }

            // 记录测试结果
            AddConcurrentTestResult(testResult);

            if (_enableConcurrentDetailedLogging)
            {
                LogConcurrentTestResult(testResult);
            }

            StorageManager.DestroyInstance(storage.InstanceId);
            LogTestInfo("读写并发测试完成");
        }

        /// <summary>
        /// 缓存并发测试协程
        /// 测试内存缓存在并发访问时的数据一致性
        /// </summary>
        private IEnumerator RunCacheConcurrencyTestCoroutine()
        {
            LogTestSection("--- 缓存并发测试 ---");

            var storage = CreateConcurrentTestStorageInstance("CacheConcurrency");
            if (storage == null)
            {
                LogTestError("无法创建测试Storage实例");
                yield break;
            }

            var testResult = new ConcurrentTestResult("缓存并发测试")
            {
                threadCount = _concurrentThreadCount,
                operationsPerThread = _concurrentOperationsPerThread,
                totalOperations = _concurrentThreadCount * _concurrentOperationsPerThread
            };

            var counter = new ThreadSafeCounter();
            var exceptionCollector = new ConcurrentExceptionCollector();

            var startTime = Time.realtimeSinceStartup;
            var memoryBefore = GC.GetTotalMemory(false);

            // 创建高频缓存操作的并发任务
            var tasks = new Task[_concurrentThreadCount];
            var countdown = new CountdownEvent(_concurrentThreadCount);

            for (int threadId = 0; threadId < _concurrentThreadCount; ++threadId)
            {
                int capturedThreadId = threadId;

                tasks[threadId] = Task.Run(() =>
                {
                    try
                    {
                        for (int i = 0; i < testResult.operationsPerThread; ++i)
                        {
                            try
                            {
                                // 高频读写操作测试缓存锁的性能
                                string key = $"cache_test_{i % 50}"; // 使用有限的键集合增加冲突

                                // 写操作
                                storage.Set(key, $"Thread{capturedThreadId}_Value{i}");

                                // 立即读取验证
                                if (storage.TryGet(key, out string value))
                                {
                                    counter.IncrementSuccess();
                                }
                                else
                                {
                                    counter.IncrementFailure();
                                }

                                // 检查键是否存在
                                if (storage.ContainsKey(key))
                                {
                                    counter.IncrementSuccess();
                                }
                                else
                                {
                                    counter.IncrementFailure();
                                }

                                // 偶尔删除数据测试删除操作的并发安全性
                                if (i % 100 == 99)
                                {
                                    storage.Remove(key);
                                }
                            }
                            catch (Exception ex)
                            {
                                exceptionCollector.AddException(ex);
                                counter.IncrementFailure();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        exceptionCollector.AddException(ex);
                    }
                    finally
                    {
                        countdown.Signal();
                    }
                });
            }

            // 等待所有线程完成
            bool completed = false;
            float timeoutTime = Time.realtimeSinceStartup + _concurrentTestTimeout;

            while (!completed && Time.realtimeSinceStartup < timeoutTime)
            {
                completed = countdown.IsSet;
                yield return null;
            }

            var executionTime = Time.realtimeSinceStartup - startTime;
            var memoryAfter = GC.GetTotalMemory(false);

            // 收集测试结果
            testResult.executionTime = executionTime;
            testResult.successfulOperations = counter.SuccessCount;
            testResult.failedOperations = counter.FailureCount;
            testResult.operationsPerSecond = testResult.totalOperations / executionTime;
            testResult.memoryUsedBytes = memoryAfter - memoryBefore;

            if (completed && !exceptionCollector.HasExceptions)
            {
                testResult.passed = true;
            }
            else
            {
                testResult.passed = false;
                if (!completed)
                {
                    testResult.errorMessage = "测试超时";
                }
                else
                {
                    var exceptions = exceptionCollector.GetAllExceptions();
                    testResult.errorMessage = $"发生{exceptions.Count}个异常: {exceptions[0].Message}";
                }
            }

            // 记录测试结果
            AddConcurrentTestResult(testResult);

            if (_enableConcurrentDetailedLogging)
            {
                LogConcurrentTestResult(testResult);
            }

            StorageManager.DestroyInstance(storage.InstanceId);
            LogTestInfo("缓存并发测试完成");
        }

        /// <summary>
        /// 文件操作并发测试协程
        /// 测试同一文件的并发读写安全性和文件锁的正确使用
        /// </summary>
        private IEnumerator RunFileOperationConcurrencyTestCoroutine()
        {
            LogTestSection("--- 文件操作并发测试 ---");

            var testResult = new ConcurrentTestResult("文件操作并发测试")
            {
                threadCount = _concurrentThreadCount,
                operationsPerThread = _concurrentOperationsPerThread / 4, // 减少文件操作频率
                totalOperations = _concurrentThreadCount * (_concurrentOperationsPerThread / 4)
            };

            var counter = new ThreadSafeCounter();
            var exceptionCollector = new ConcurrentExceptionCollector();
            var storageInstances = new StorageInstance[_concurrentThreadCount];

            // 创建多个存储实例，但使用相同的文件路径测试文件锁
            for (int i = 0; i < _concurrentThreadCount; ++i)
            {
                storageInstances[i] = CreateConcurrentPersistentStorageInstance("SharedFileTest");
                if (storageInstances[i] == null)
                {
                    LogTestError($"无法创建存储实例 {i}");
                    yield break;
                }
            }

            var startTime = Time.realtimeSinceStartup;
            var memoryBefore = GC.GetTotalMemory(false);

            // 创建文件操作并发任务
            var tasks = new Task[_concurrentThreadCount];
            var countdown = new CountdownEvent(_concurrentThreadCount);

            for (int threadId = 0; threadId < _concurrentThreadCount; ++threadId)
            {
                int capturedThreadId = threadId;
                StorageInstance capturedStorage = storageInstances[threadId];

                tasks[threadId] = Task.Run(() =>
                {
                    try
                    {
                        for (int i = 0; i < testResult.operationsPerThread; ++i)
                        {
                            try
                            {
                                // 写入数据到缓存
                                string key = $"file_test_{capturedThreadId}_{i}";
                                string value = $"FileTestData_{capturedThreadId}_{i}_{DateTime.Now.Ticks}";
                                capturedStorage.Set(key, value);

                                // 异步保存到文件
                                bool saveCompleted = false;
                                string saveError = null;
                                capturedStorage.SaveToFileAsync((success, errorMessage) =>
                                {
                                    if (success)
                                    {
                                        counter.IncrementSuccess();
                                    }
                                    else
                                    {
                                        counter.IncrementFailure();
                                        saveError = errorMessage;
                                    }
                                    saveCompleted = true;
                                });

                                // 等待异步保存完成
                                int waitCount = 0;
                                while (!saveCompleted && waitCount < 1000)
                                {
                                    Thread.Sleep(1);
                                    waitCount++;
                                }

                                if (!saveCompleted)
                                {
                                    counter.IncrementFailure();
                                }

                                // 异步加载文件
                                bool loadCompleted = false;
                                capturedStorage.LoadFromFileAsync((success, data, errorMessage) =>
                                {
                                    if (success)
                                    {
                                        counter.IncrementSuccess();
                                    }
                                    else
                                    {
                                        counter.IncrementFailure();
                                    }
                                    loadCompleted = true;
                                });

                                // 等待异步加载完成
                                waitCount = 0;
                                while (!loadCompleted && waitCount < 1000)
                                {
                                    Thread.Sleep(1);
                                    waitCount++;
                                }

                                if (!loadCompleted)
                                {
                                    counter.IncrementFailure();
                                }
                            }
                            catch (Exception ex)
                            {
                                exceptionCollector.AddException(ex);
                                counter.IncrementFailure();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        exceptionCollector.AddException(ex);
                    }
                    finally
                    {
                        countdown.Signal();
                    }
                });
            }

            // 等待所有线程完成
            bool completed = false;
            float timeoutTime = Time.realtimeSinceStartup + _concurrentTestTimeout * 2; // 文件操作需要更长时间

            while (!completed && Time.realtimeSinceStartup < timeoutTime)
            {
                completed = countdown.IsSet;
                yield return null;
            }

            var executionTime = Time.realtimeSinceStartup - startTime;
            var memoryAfter = GC.GetTotalMemory(false);

            // 收集测试结果
            testResult.executionTime = executionTime;
            testResult.successfulOperations = counter.SuccessCount;
            testResult.failedOperations = counter.FailureCount;
            testResult.operationsPerSecond = testResult.totalOperations / executionTime;
            testResult.memoryUsedBytes = memoryAfter - memoryBefore;

            if (completed && !exceptionCollector.HasExceptions)
            {
                testResult.passed = true;
            }
            else
            {
                testResult.passed = false;
                if (!completed)
                {
                    testResult.errorMessage = "测试超时";
                }
                else
                {
                    var exceptions = exceptionCollector.GetAllExceptions();
                    testResult.errorMessage = $"发生{exceptions.Count}个异常: {exceptions[0].Message}";
                }
            }

            // 记录测试结果
            AddConcurrentTestResult(testResult);

            if (_enableConcurrentDetailedLogging)
            {
                LogConcurrentTestResult(testResult);
            }

            // 清理所有存储实例
            for (int i = 0; i < _concurrentThreadCount; ++i)
            {
                if (storageInstances[i] != null)
                {
                    StorageManager.DestroyInstance(storageInstances[i].InstanceId);
                }
            }

            LogTestInfo("文件操作并发测试完成");
        }

        /// <summary>
        /// TypeWrapper性能测试协程
        /// 验证TypeWrapper系统在并发环境下避免装箱/拆箱的效果
        /// </summary>
        private IEnumerator RunTypeWrapperPerformanceTestCoroutine()
        {
            LogTestSection("--- TypeWrapper性能测试 ---");

            var storage = CreateConcurrentTestStorageInstance("TypeWrapperPerformance");
            if (storage == null)
            {
                LogTestError("无法创建测试Storage实例");
                yield break;
            }

            var testResult = new ConcurrentTestResult("TypeWrapper性能测试")
            {
                threadCount = _concurrentThreadCount,
                operationsPerThread = _concurrentOperationsPerThread * 2, // 增加操作数以测试性能
                totalOperations = _concurrentThreadCount * (_concurrentOperationsPerThread * 2)
            };

            var counter = new ThreadSafeCounter();
            var exceptionCollector = new ConcurrentExceptionCollector();

            var startTime = Time.realtimeSinceStartup;
            var memoryBefore = GC.GetTotalMemory(false);

            // 创建TypeWrapper性能测试的并发任务
            var tasks = new Task[_concurrentThreadCount];
            var countdown = new CountdownEvent(_concurrentThreadCount);

            for (int threadId = 0; threadId < _concurrentThreadCount; ++threadId)
            {
                int capturedThreadId = threadId;

                tasks[threadId] = Task.Run(() =>
                {
                    try
                    {
                        for (int i = 0; i < testResult.operationsPerThread; ++i)
                        {
                            try
                            {
                                // 测试各种基础类型的TypeWrapper性能
                                string keyPrefix = $"perf_{capturedThreadId}_{i}";

                                // 整数类型测试
                                storage.Set($"{keyPrefix}_int", i);
                                if (storage.TryGet($"{keyPrefix}_int", out int intValue) && intValue == i)
                                {
                                    counter.IncrementSuccess();
                                }
                                else
                                {
                                    counter.IncrementFailure();
                                }

                                // 浮点数类型测试
                                float floatVal = i * 3.14f;
                                storage.Set($"{keyPrefix}_float", floatVal);
                                if (storage.TryGet($"{keyPrefix}_float", out float floatValue) &&
                                    Mathf.Approximately(floatValue, floatVal))
                                {
                                    counter.IncrementSuccess();
                                }
                                else
                                {
                                    counter.IncrementFailure();
                                }

                                // 字符串类型测试
                                string stringVal = $"String_{capturedThreadId}_{i}";
                                storage.Set($"{keyPrefix}_string", stringVal);
                                if (storage.TryGet($"{keyPrefix}_string", out string stringValue) &&
                                    stringValue == stringVal)
                                {
                                    counter.IncrementSuccess();
                                }
                                else
                                {
                                    counter.IncrementFailure();
                                }

                                // 布尔类型测试
                                bool boolVal = i % 2 == 0;
                                storage.Set($"{keyPrefix}_bool", boolVal);
                                if (storage.TryGet($"{keyPrefix}_bool", out bool boolValue) && boolValue == boolVal)
                                {
                                    counter.IncrementSuccess();
                                }
                                else
                                {
                                    counter.IncrementFailure();
                                }

                                // Unity类型测试
                                Vector3 vectorVal = new Vector3(i, i * 2, i * 3);
                                storage.Set($"{keyPrefix}_vector3", vectorVal);
                                if (storage.TryGet($"{keyPrefix}_vector3", out Vector3 vectorValue) &&
                                    Vector3.Distance(vectorValue, vectorVal) < 0.001f)
                                {
                                    counter.IncrementSuccess();
                                }
                                else
                                {
                                    counter.IncrementFailure();
                                }

                                // 复杂对象类型测试
                                var complexObj = new SimpleTestClass(i, i * 2.5f, $"Complex_{capturedThreadId}_{i}", i % 2 == 0);
                                storage.Set($"{keyPrefix}_complex", complexObj);
                                if (storage.TryGet($"{keyPrefix}_complex", out SimpleTestClass complexValue) &&
                                    complexValue.Equals(complexObj))
                                {
                                    counter.IncrementSuccess();
                                }
                                else
                                {
                                    counter.IncrementFailure();
                                }

                                // 类型转换测试（测试TypeWrapper的转换能力）
                                storage.Set($"{keyPrefix}_convert_test", i);
                                if (storage.TryGet($"{keyPrefix}_convert_test", out float convertedValue) &&
                                    Mathf.Approximately(convertedValue, i))
                                {
                                    counter.IncrementSuccess();
                                }
                                else
                                {
                                    counter.IncrementFailure();
                                }
                            }
                            catch (Exception ex)
                            {
                                exceptionCollector.AddException(ex);
                                counter.IncrementFailure();
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        exceptionCollector.AddException(ex);
                    }
                    finally
                    {
                        countdown.Signal();
                    }
                });
            }

            // 等待所有线程完成
            bool completed = false;
            float timeoutTime = Time.realtimeSinceStartup + _concurrentTestTimeout;

            while (!completed && Time.realtimeSinceStartup < timeoutTime)
            {
                completed = countdown.IsSet;
                yield return null;
            }

            var executionTime = Time.realtimeSinceStartup - startTime;
            var memoryAfter = GC.GetTotalMemory(false);

            // 收集测试结果
            testResult.executionTime = executionTime;
            testResult.successfulOperations = counter.SuccessCount;
            testResult.failedOperations = counter.FailureCount;
            testResult.operationsPerSecond = testResult.totalOperations / executionTime;
            testResult.memoryUsedBytes = memoryAfter - memoryBefore;

            if (completed && !exceptionCollector.HasExceptions)
            {
                testResult.passed = true;
            }
            else
            {
                testResult.passed = false;
                if (!completed)
                {
                    testResult.errorMessage = "测试超时";
                }
                else
                {
                    var exceptions = exceptionCollector.GetAllExceptions();
                    testResult.errorMessage = $"发生{exceptions.Count}个异常: {exceptions[0].Message}";
                }
            }

            // 记录测试结果
            AddConcurrentTestResult(testResult);

            if (_enableConcurrentDetailedLogging)
            {
                LogConcurrentTestResult(testResult);
            }

            // 额外的性能统计信息
            LogTestInfo($"TypeWrapper性能统计:");
            LogTestInfo($"  总操作数: {testResult.totalOperations}");
            LogTestInfo($"  操作速度: {testResult.operationsPerSecond:F0} 操作/秒");
            LogTestInfo($"  内存使用: {testResult.memoryUsedBytes / 1024.0:F1} KB");
            LogTestInfo($"  平均每操作内存: {(double)testResult.memoryUsedBytes / testResult.totalOperations:F2} 字节");

            StorageManager.DestroyInstance(storage.InstanceId);
            LogTestInfo("TypeWrapper性能测试完成");
        }

        #endregion
    }
}